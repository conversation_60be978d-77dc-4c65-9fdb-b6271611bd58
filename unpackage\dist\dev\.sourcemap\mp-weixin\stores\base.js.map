{"version": 3, "file": "base.js", "sources": ["stores/base.js"], "sourcesContent": ["// stores/base.js\nimport { defineStore } from 'pinia';\nimport { ref } from 'vue'\nexport const useBaseStore = defineStore('base', () => {\r\n\tconst miniProjectName = \"河南农大微门户\";\n\tconst count = ref(0);\n\tfunction increment() {\n\t\tcount.value++;\n\t}\n\n\treturn { miniProjectName,count, increment };\n});\n\n"], "names": ["defineStore", "ref"], "mappings": ";;AAGY,MAAC,eAAeA,cAAAA,YAAY,QAAQ,MAAM;AACrD,QAAM,kBAAkB;AACxB,QAAM,QAAQC,kBAAI,CAAC;AACnB,WAAS,YAAY;AACpB,UAAM;AAAA,EACN;AAED,SAAO,EAAE,iBAAgB,OAAO;AACjC,CAAC;;"}