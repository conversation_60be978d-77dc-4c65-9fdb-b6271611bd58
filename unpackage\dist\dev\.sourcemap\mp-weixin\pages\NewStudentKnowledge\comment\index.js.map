{"version": 3, "file": "index.js", "sources": ["pages/NewStudentKnowledge/comment/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9jb21tZW50L2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"page-container\">\n\t\t<button class=\"contributor-button\" @tap=\"showContributors\">特别鸣谢</button>\n\n\t\t<view class=\"content-wrapper\">\n\t\t\t<view class=\"header-section\">\n\t\t\t\t<text class=\"section-title\">问答专栏</text>\n\t\t\t</view>\n\t\t\t<view class=\"qa-item\" v-for=\"(item, index) in registrationQa\" :key=\"'reg-' + index\">\n\t\t\t\t<text class=\"question\">{{ item.question }}</text>\n\t\t\t\t<text class=\"answer\">\n\t\t\t\t\t<text v-for=\"(part, pIndex) in parseAnswer(item.answer)\" :key=\"pIndex\" :class=\"{ 'highlight': part.type === 'highlight' }\">{{ part.content }}</text>\n\t\t\t\t</text>\n\t\t\t\t\n\t\t\t\t<view v-if=\"item.image_url != null\" @tap=previewImage(item.image_url)>\n\t\t\t\t\t<img class=\"image\" :src=item.image_url  alt=\"校历图片\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"custom-modal-overlay\" v-if=\"showContributorModal\" @tap=\"closeContributorsModal\">\n\t\t\t<view class=\"custom-modal-content\" @tap.stop>\n\t\t\t\t<text class=\"modal-title\">贡献者名单(不分先后)</text>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-contributors-list\">\n\t\t\t\t\t<!-- 遍历贡献者类别 -->\n\t\t\t\t\t<view v-for=\"(category, catIndex) in contributors\" :key=\"'cat-' + catIndex\" class=\"contributor-category\">\n\t\t\t\t\t\t<text class=\"category-title\">{{ category.category }}</text>\n\t\t\t\t\t\t<view class=\"names-grid\">\n\t\t\t\t\t\t\t<text class=\"contributor-name\" v-for=\"(name, nameIndex) in category.names\" :key=\"'name-' + nameIndex\">{{ name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"modal-button\" @tap=\"closeContributorsModal\">关闭</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"scroll-controls\">\n\t\t\t<text class=\"reading-progress\">{{ Math.round(readingProgress) }}%</text>\n\t\t\t<button class=\"back-to-top-button\" @click=\"scrollToTop\">\n\t\t\t\t<image class=\"top-arrow-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6893244343dd4_1754473539.png\"></image>\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t\t<view class=\"bottom\">\n\t\t\t技术支持 河南农业大学IT工作室\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onPageScroll, onReady } from '@dcloudio/uni-app';\nimport qa from './Q&A.json'\n\n// 贡献者名单弹窗逻辑\nconst showContributorModal = ref(false);\n// 动态贡献者名单，现在分为类别\nconst contributors = ref([\n\t{ category: '河南农业大学IT工作室成员', names: ['24级CLOWN','24级千木','24级非酋sama','24级XX','24级斯特','24级阿布学长','24级槿熙','24级昔文', '24级Pinging'] },\n\t{ category: '其他贡献者', names: ['22级卢荟胶', '23级鲤鱼'] }\n]);\n\nconst showContributors = () => {\n\tshowContributorModal.value = true;\n};\nconst closeContributorsModal = () => {\n\tshowContributorModal.value = false;\n};\n\n// 阅读进度和返回顶部逻辑\nconst readingProgress = ref(0);\nlet totalScrollHeight = 0; // 页面总可滚动高度\r\n\r\n\n\nonReady(() => {\n\t// 页面渲染完成后，获取内容区域的总高度\n\tuni.createSelectorQuery().select('.page-container').boundingClientRect(pageRect => {\n\t\tif (pageRect) {\n\t\t\t// totalScrollHeight = 实际内容高度 - 视口高度\n\t\t\ttotalScrollHeight = pageRect.height - uni.getSystemInfoSync().windowHeight;\n\t\t\tif (totalScrollHeight < 0) totalScrollHeight = 0; // 防止负值\n\t\t}\n\t}).exec();\n});\n\nonPageScroll((e) => {\n\tif (totalScrollHeight > 0) {\n\t\treadingProgress.value = (e.scrollTop / totalScrollHeight) * 100;\n\t\tif (readingProgress.value > 100) readingProgress.value = 100; // 确保不超过100%\n\t} else {\n\t\treadingProgress.value = 0;\n\t}\n});\n\nconst scrollToTop = () => {\n\tuni.pageScrollTo({\n\t\tscrollTop: 0,\n\t\tduration: 300\n\t});\n};\n\n// 解析答案字符串，将高亮部分分离\nconst parseAnswer = (answerString) => {\n    const parts = [];\n    const regex = /<text class=\"highlight\">(.*?)<\\/text>/g;\n    let lastIndex = 0;\n    let match;\n\n    while ((match = regex.exec(answerString)) !== null) {\n        // Add plain text before the highlight\n        if (match.index > lastIndex) {\n            parts.push({ type: 'text', content: answerString.substring(lastIndex, match.index) });\n        }\n        // Add highlight text\n        parts.push({ type: 'highlight', content: match[1] });\n        lastIndex = regex.lastIndex;\n    }\n\n    // Add any remaining plain text after the last highlight\n    if (lastIndex < answerString.length) {\n        parts.push({ type: 'text', content: answerString.substring(lastIndex) });\n    }\n    return parts;\n};\n\n// 问答数据 (已从模板中提取到 script 部分，便于管理和维护)\nconst registrationQa = ref(qa);\n\n\n\n// 图片预览逻辑已修改为跳转到详情页\nconst previewImage = (imageUrl) => {\n\tuni.navigateTo({\n\t\turl: `/pages/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`,\n\t});\n};\n\n\n</script>\n\n<style scoped>\n/* 页面容器 */\n.page-container {\n\twidth: 100%;\n\tmin-height: 100vh; /* 确保页面高度至少为视口高度 */\n\tbox-sizing: border-box;\n\tbackground: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);\n}\n\n\n.bottom{\n\tfont-size: 15px;\n\tfont-weight: 100;\n\ttext-align: center;\n\tcolor: #7a776f;\n}\n\n/* 内容区域包裹器 */\n.content-wrapper {\n\twidth: 90%;\n\tmargin: 0 auto;\n\tbackground: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);\n\tborder-radius: 16rpx; /* 圆角 */\n\t/* 调整内容区域的内边距，左右各增加到30rpx */\n\tpadding: 100rpx 30rpx 30rpx 30rpx; /* 上、右、下、左 */\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */\n}\n\n.contributor-button {\n\tposition: absolute; /* 固定定位 */\n\ttop: 60rpx; /* 距离顶部60rpx */\n\tright: 30rpx; /* 距离右侧30rpx */\n\tbackground-color: transparent; /* 透明背景 */\n\tcolor: #000000; /* 黑色文字 */\n\tfont-size: 24rpx;\n\tpadding: 10rpx 20rpx;\n\tborder: 2rpx solid #000000; /* 黑色边框 */\n\tborder-radius: 40rpx; /* 圆角 */\n\tline-height: 1; /* 调整行高使按钮文字垂直居中 */\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\n\ttransition: all 0.2s ease;\n\tz-index: 100; /* 确保在内容之上 */\n}\n\n.contributor-button:active,\n.contributor-button:hover {\n\tbackground-color: #000000; /* 悬停/点击时的背景色 */\n\tcolor: #ffffff; /* 悬停/点击时的文字颜色 */\n\ttransform: scale(1.05);\n}\n\n/* 标题样式 */\n.header-section {\n\tmargin: 100px;\n\ttext-align: center;\n\tmargin: 40rpx auto 30rpx; /* 上下边距，左右居中 */\n\tborder-bottom: 2rpx solid #338174; /* 底部边框 */\n\tpadding-bottom: 10rpx; /* 边框与文字间距 */\n\tdisplay: table; /* 使边框只包裹内容宽度 */\n}\n.image{\n\twidth: 100%;\n}\n\n.section-title {\n\tmargin: 100px;\n\tfont-size: 33.6rpx;\n\tfont-weight: bold;\n\tcolor: #000000; /* 标题颜色改为深蓝色，与主题色保持一致 */\n\tline-height: 1.75;\n\twhite-space: nowrap; /* 防止标题换行 */\n}\n\n/* 问答项样式 */\n.qa-item {\n\tmargin: 30rpx 0;\n\tline-height: 1.75;\n\tfont-size: 28rpx;\n\tletter-spacing: 0.05em;\n\tcolor: #333333;\n\ttext-align: justify;\n}\n\n.question {\n\tfont-weight: bold;\n\tcolor: #55aa00; /* 绿色问题 */\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.answer {\n\tdisplay: block;\n}\n\n.highlight {\n\tcolor: #0f4c81; /* 高亮颜色改为深蓝色，与主题色保持一致 */\n\tfont-weight: bold;\n}\n\n\n\n\n/* 贡献者名单弹窗样式 */\n.custom-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.7); /* 更深的半透明背景，突出弹窗 */\n\tz-index: 101;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tanimation: fadeIn 0.3s ease-out;\n}\n\n.custom-modal-content {\n\tbackground-color: #ffffff;\n\tborder-radius: 28rpx; /* 稍微更大的圆角 */\n\tpadding: 60rpx; /* 增加内边距 */\n\tmargin: 40rpx;\n\tmax-width: 650rpx; /* 稍微增加最大宽度 */\n\twidth: 90%;\n\tbox-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.25); /* 更明显的阴影 */\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\ttext-align: center;\n\tanimation: slideIn 0.3s ease-out;\n\tborder: 1rpx solid rgba(200, 200, 200, 0.3); /* 增加一个细微的边框 */\n}\n\n.modal-title {\n\tfont-size: 42rpx; /* 标题字号更大 */\n\tfont-weight: bold;\n\tcolor: #000000;\n\tmargin-bottom: 35rpx; /* 增加与列表的间距 */\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.05); /* 标题轻微文字阴影 */\n}\n\n/* 新增：贡献者类别容器 */\n.contributor-category {\n\twidth: 100%;\n\tmargin-bottom: 40rpx; /* 类别之间的间距 */\n}\n\n/* 新增：类别标题 */\n.category-title {\n\tfont-size: 32rpx; /* 类别标题字号 */\n\tfont-weight: bold;\n\tcolor: #609c63; /* 类别标题颜色 */\n\tmargin-bottom: 20rpx; /* 标题与名字的间距 */\n\tdisplay: block; /* 确保标题独占一行 */\n\ttext-align: center;\n\tborder-bottom: 2rpx solid #e0e0e0; /* 底部细线 */\n\tpadding-bottom: 10rpx;\n}\n\n/* 新增：名字网格布局 */\n.names-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap; /* 允许换行 */\n\tjustify-content: center; /* 名字居中对齐 */\n\tgap: 20rpx 30rpx; /* 行间距和列间距 */\n}\n\n.contributor-name {\n\tfont-size: 30rpx; /* 贡献者名字字号稍大 */\n\tcolor: #4a4a4a; /* 更深的灰色 */\n\t/* 移除原来的 margin，使用 gap 控制间距 */\n\tline-height: 1.6;\n\tpadding: 5rpx 0; /* 增加垂直内边距 */\n\ttransition: color 0.2s ease;\n\t/* white-space: nowrap; */ /* 允许名字内部换行，如果名字过长 */\n}\n\n\n.modal-button {\n\tbackground:#609c63; \n\tcolor: #fff;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx; /* 按钮文字稍大 */\n\theight: 80rpx; /* 按钮高度增加 */\n\twidth: 85%; /* 按钮宽度稍大 */\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 8rpx 20rpx rgba(15, 76, 129, 0.4); /* 更明显的阴影，与背景色匹配 */\n\ttransition: all 0.3s ease-in-out; /* 更平滑的过渡效果 */\n\tborder: none;\n\tletter-spacing: 1rpx; /* 增加文字间距 */\n}\n\n.modal-button:active {\n\ttransform: translateY(4rpx); /* 点击时下沉效果更明显 */\n\tbox-shadow: 0 4rpx 10rpx rgba(15, 76, 129, 0.3); /* 点击时阴影变小 */\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 返回顶部按钮和阅读进度样式 */\n.scroll-controls {\n\tposition: fixed;\n\tbottom: 130rpx; /* 距离底部 */\n\tright: 20rpx; /* 距离右侧 */\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tz-index: 99; /* 确保在内容之上，但在弹窗之下 */\n}\n\n.reading-progress {\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tfont-size: 24rpx;\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 30rpx;\n\tmargin-bottom: 10rpx;\n\twhite-space: nowrap; /* 防止百分比换行 */\n}\n\n.back-to-top-button {\n\tbackground-color: #0f4c81; /* 匹配主题色 */\n\tcolor: #fff;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%; /* 圆形按钮 */\n\tdisplay: flex; /* 使用flexbox来居中图片 */\n\tjustify-content: center;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);\n\tborder: none;\n\tpadding: 0; /* 移除默认padding */\n}\n\n.top-arrow-icon {\n\twidth: 80rpx; /* 图片大小 */\n\theight: 80rpx;\n\t/* 可以添加滤镜来改变颜色，如果图片是黑色的 */\n\t/* filter: invert(100%); */\n}\n\n/* 动画效果 (弹窗) */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t}\n\tto {\n\t\topacity: 1;\n\t}\n}\n\n@keyframes slideIn {\n\tfrom {\n\t\ttransform: translateY(-50px);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t\topacity: 1;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/comment/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onReady", "uni", "onPageScroll"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,UAAA,uBAAAA,cAAAA,IAAA,KAAA;AAEA,UAAA,eAAAA,cAAAA,IAAA;AAAA,MACA,EAAA,UAAA,iBAAA,OAAA,CAAA,YAAA,SAAA,aAAA,SAAA,SAAA,WAAA,SAAA,SAAA,YAAA,EAAA;AAAA,MACA,EAAA,UAAA,SAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AAAA,IACA,CAAA;AAEA,UAAA,mBAAA,MAAA;AACA,2BAAA,QAAA;AAAA,IACA;AACA,UAAA,yBAAA,MAAA;AACA,2BAAA,QAAA;AAAA,IACA;AAGA,UAAA,kBAAAA,cAAAA,IAAA,CAAA;AACA,QAAA,oBAAA;AAIAC,kBAAAA,QAAA,MAAA;AAEAC,oBAAA,MAAA,oBAAA,EAAA,OAAA,iBAAA,EAAA,mBAAA,cAAA;AACA,YAAA,UAAA;AAEA,8BAAA,SAAA,SAAAA,cAAA,MAAA,kBAAA,EAAA;AACA,cAAA,oBAAA;AAAA,gCAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,KAAA;AAAA,IACA,CAAA;AAEAC,kBAAA,aAAA,CAAA,MAAA;AACA,UAAA,oBAAA,GAAA;AACA,wBAAA,QAAA,EAAA,YAAA,oBAAA;AACA,YAAA,gBAAA,QAAA;AAAA,0BAAA,QAAA;AAAA,MACA,OAAA;AACA,wBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,cAAA,MAAA;AACAD,oBAAAA,MAAA,aAAA;AAAA,QACA,WAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,cAAA,CAAA,iBAAA;AACA,YAAA,QAAA,CAAA;AACA,YAAA,QAAA;AACA,UAAA,YAAA;AACA,UAAA;AAEA,cAAA,QAAA,MAAA,KAAA,YAAA,OAAA,MAAA;AAEA,YAAA,MAAA,QAAA,WAAA;AACA,gBAAA,KAAA,EAAA,MAAA,QAAA,SAAA,aAAA,UAAA,WAAA,MAAA,KAAA,EAAA,CAAA;AAAA,QACA;AAEA,cAAA,KAAA,EAAA,MAAA,aAAA,SAAA,MAAA,CAAA,EAAA,CAAA;AACA,oBAAA,MAAA;AAAA,MACA;AAGA,UAAA,YAAA,aAAA,QAAA;AACA,cAAA,KAAA,EAAA,MAAA,QAAA,SAAA,aAAA,UAAA,SAAA,EAAA,CAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA;AAGA,UAAA,iBAAAF,cAAAA,IAAA,EAAA;AAKA,UAAA,eAAA,CAAA,aAAA;AACAE,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,iCAAA,mBAAA,QAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzIA,GAAG,WAAW,eAAe;"}