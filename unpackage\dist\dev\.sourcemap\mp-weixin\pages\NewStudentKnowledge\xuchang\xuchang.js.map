{"version": 3, "file": "xuchang.js", "sources": ["pages/NewStudentKnowledge/xuchang/xuchang.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS94dWNoYW5nL3h1Y2hhbmcudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 顶部状态栏占位，适配刘海屏 -->\n    <view class=\"status-bar-placeholder\"></view>\n\n    <scroll-view class=\"main-scroll-view\" scroll-y=\"true\">\n      <view class=\"content-wrapper\">\n        <!-- 欢迎标题 -->\n        <view class=\"welcome-title-wrapper\">\n          <text class=\"welcome-title\">欢迎来到河南农业大学许昌校区</text>\n        </view>\n\n        <!-- 新同学问候 -->\n        <view class=\"greeting-section\">\n          <text class=\"greeting-text\">你好，新同学：</text>\n        </view>\n\n        <!-- 一、校区基础信息 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">一、校区基础信息</text>\n        </view>\n\n        <view class=\"info-paragraph\">\n          <text class=\"info-strong\">1. 地理位置</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 校区地址：河南省许昌市建安区河南农业大学\n            <text class=\"newline\"/>• 生活配套：\n            <text class=\"newline\"/>▪ 校内：食堂、超市、快递站（支持主流快递，网购前记得\n            <text class=\"info-strong-blue\">切换常用地址</text>）\n            <text class=\"newline\"/>▪ 校外：北海胖东来商圈、市中心（公交直达），校门口夜间设小吃摊。\n          </text>\n          <text class=\"info-strong\">2.交通路线</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>•家长来送：直接导航河南省许昌市建安区河南农业大学许昌校区\n            <text class=\"newline\"/>•坐火车（一般是在许昌站下车）：乘坐\n            <text class=\"info-strong-blue\">Y66路</text>公交车(运营时间：6:00～19:00)、打车或者其他\n            <text class=\"newline\"/>•坐高铁（一般是在许昌东站下车）：乘坐\n            <text class=\"info-strong-blue\">29路</text>公交车(运营时间：6:00～19:00)、打车或者其他\n          </text>\n        </view>\n\n        <!-- 二、宿舍与生活 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">二、宿舍与生活</text>\n        </view>\n\n        <view class=\"info-list\">\n          <view class=\"list-item\">\n            <text class=\"list-item-number\">3.</text>\n            <view class=\"list-item-content\">\n              <text class=\"info-strong\">宿舍配置</text>\n              <text class=\"info-text\">\n                <text class=\"newline\"/>• 房型：4-6人间（随机分配），新楼配备\n                <text class=\"info-strong-blue\">独卫</text>（仅冷水），老楼无独立卫浴。\n                <text class=\"newline\"/>• 设施：\n                <text class=\"newline\"/>▪ 统一配置\n                <text class=\"info-strong-blue\">空调</text>（需付租金）、\n                <text class=\"info-strong-blue\">暖气</text>、\n                <text class=\"info-strong-blue\">上床下桌</text>。\n                <text class=\"newline\"/>▪ 床铺尺寸：0.9m×2.0m，允许军训后挂床帘。\n                <text class=\"newline\"/>• 水电：24小时不断电，电费超额需自行充值（微信公众号“河南农业大学信息化办公室”办理，充值前请明晰你在哪个寝室）。\n              </text>\n            </view>\n          </view>\n          <view class=\"list-item\">\n            <text class=\"list-item-number\">4.</text>\n            <view class=\"list-item-content\">\n              <text class=\"info-strong\">洗浴与饮水</text>\n              <text class=\"info-text\">\n                <text class=\"newline\"/>• 澡堂为单间隔间（使用“胖乖生活”APP，为避免在澡堂被硬控半天，尽量在洗澡前下载好），新楼独卫无热水仅限冷水洗漱。\n                <text class=\"newline\"/>• 每层设饮水机，扫码支付使用，无需办理水卡。\n              </text>\n            </view>\n          </view>\n          <view class=\"list-item\">\n            <text class=\"list-item-number\">5.</text>\n            <view class=\"list-item-content\">\n              <text class=\"info-strong\">违禁事项</text>\n              <text class=\"info-text\">\n                <text class=\"newline\"/>• 禁止使用吹风机、电煮锅等大功率电器（触发跳闸并通报）。\n                <text class=\"newline\"/>• 禁止私接电器，学校不定期抽查。\n              </text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 三、学习与校园管理 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">三、学习与校园管理</text>\n        </view>\n\n        <view class=\"info-paragraph\">\n          <text class=\"info-strong\">6. 课程与考试</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 早晚自习：软件学院固定晚自习，其他学院依安排而定。\n            <text class=\"newline\"/>• 挂科风险：专业课需认真听讲+课后练习，突击备考可能挂科。\n            <text class=\"newline\"/>• 转专业：\n            <text class=\"newline\"/>▪ 大一下学期开放申请（需笔试/面试），无挂科、无违纪可申请。\n            <text class=\"newline\"/>▪ 原则：不可跨校区转专业（如许昌→郑州），参军退伍后可自由转专业。\n          </text>\n          <text class=\"info-strong\">7. 网络与电子设备</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 校园网：仅支持移动网络，建议办理校园卡提升网速（非强制，与选课无关）。\n            <text class=\"newline\"/>• 电脑要求：计算机类专业必需，其他专业建议配置（5-6K性价比机型足够）。\n          </text>\n          <text class=\"info-strong\">8. 校园服务</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 校医院：处理感冒、外伤等常见病，校外转诊可报销（具体流程入校后通知）。\n            <text class=\"newline\"/>• 快递站：校内集中收发点，开学季避免寄大件（易堆积）。\n          </text>\n        </view>\n\n        <!-- 四、入学报到须知 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">四、入学报到须知</text>\n        </view>\n\n        <view class=\"info-paragraph\">\n          <text class=\"info-strong\">9. 必备材料</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 录取通知书、身份证及复印件、学籍档案、一寸/二寸证件照（蓝白底各12张+4张）。\n            <text class=\"newline\"/>• 无需携带现金（学费/生活费存银行卡），校园卡开学统一发放（就餐/门禁/洗澡一卡通）。\n          </text>\n          <text class=\"info-strong\">10. 报到流程</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 时间：仅限报到当日（不可提前入住），建议外省学生提前预订周边酒店。\n            <text class=\"newline\"/>• 系统：8月底开放迎新系统（yxxt.henau.edu.cn），完成信息填报、入学答题、军训服购买。\n            <text class=\"newline\"/>• 联系：8月中下旬分配助班并建立班级群（通知学号、宿舍等信息）。\n          </text>\n          <text class=\"info-strong\">11. 军训安排</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 时长约2周（含周末），统一购买军训服（自备腰带防松垮）。\n            <text class=\"newline\"/>• 内容：军姿、跑操等基础训练，注意防晒并及时反馈身体不适。\n          </text>\n        </view>\n\n        <!-- 五、校园资源与支持 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">五、校园资源与支持</text>\n        </view>\n\n        <view class=\"info-paragraph\">\n          <text class=\"info-strong\">12. 资助政策</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 奖学金：国家奖学金（8000元/年）、国家励志奖学金（5000元/年）、校级奖学金（最高2000元/年）。\n            <text class=\"newline\"/>• 助学金：覆盖约25%在校生，平均3300元/年。\n            <text class=\"newline\"/>• 助学贷款：最高16000元/年（在校期间免息），需学号办理（8月中旬公布）。\n            <text class=\"newline\"/>• 绿色通道：特困生可先入学后补手续。\n          </text>\n          <text class=\"info-strong\">13. 社团与活动</text>\n          <text class=\"info-text\">\n            <text class=\"newline\"/>• 社团举例：青年志愿者协会、IT工作室、羽毛球队、礼仪队、微爱公益协会等。\n            <text class=\"newline\"/>• 大型活动：军训汇演、迎新晚会、“百团大战”（社团招新）。\n          </text>\n        </view>\n\n        <!-- 六、安全提醒 -->\n        <view class=\"section-title-wrapper\">\n          <text class=\"section-title\">六、安全提醒</text>\n        </view>\n\n        <view class=\"info-paragraph\">\n          <text class=\"info-text\">\n            14.\n            <text class=\"info-strong-blue\">警惕陌生链接</text>（尤其QQ邮箱），\n            <text class=\"info-strong-blue\">不泄露身份证</text>、\n            <text class=\"info-strong-blue\">学号</text>、\n            <text class=\"info-strong-blue\">银行卡信息</text>。\n            <text class=\"newline\"/>15.\n            <text class=\"info-strong-blue\">团关系转接</text>待分班后由助班通知（8月中下旬）。\n            <text class=\"newline\"/>16. 校园\n            <text class=\"info-strong-blue\">无早操</text>但需早起、无志愿时长强制要求，晚归可能随机抽查。\n          </text>\n          <text class=\"info-text small-text\">\n            温馨提示：更多动态请关注河南农业大学官网及招生办热线（0371-56990360/56990366）\n            <text class=\"newline\"/>该信息皆有在校学生回答，请注意甄别未经证实的信息。\n          </text>\n        </view>\n      </view>\n\t  \n\t  \n\t <!-- 返回顶部按钮和阅读进度 -->\n\t <view class=\"scroll-controls\">\n\t \t<text class=\"reading-progress\">{{ Math.round(readingProgress) }}%</text>\n\t \t<button class=\"back-to-top-button\" @click=\"scrollToTop\">\n\t \t\t<!-- 使用图片作为返回顶部图标 -->\n\t \t\t<image class=\"top-arrow-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6893244343dd4_1754473539.png\"></image>\n\t \t</button>\n\t </view>\n\t  \n\t  \n\t  \n    </scroll-view>\n  </view>\n  \n  \n</template>\n\n\n<script setup>\n\timport { ref } from 'vue';\n\timport { onPageScroll, onReady } from '@dcloudio/uni-app';\n\t\n\t// 跟踪阅读进度\n\tconst readingProgress = ref(0);\n\t// 页面总可滚动高度\n\tlet totalScrollHeight = 0; \n\t\n\tonReady(() => {\n\t\t// 页面渲染完成后，获取内容区域的总高度\n\t\tuni.createSelectorQuery().select('.main-scroll-view').boundingClientRect(rect => {\n\t\t\tif (rect) {\n\t\t\t\t// 获取 scroll-view 的实际内容高度\n\t\t\t\t// 注意：uni-app的scroll-view在某些情况下其boundingClientRect.height可能就是其可视高度\n\t\t\t\t// 更准确的做法是获取其内部内容的实际高度\n\t\t\t\tuni.createSelectorQuery().select('.content-wrapper').boundingClientRect(contentRect => {\n\t\t\t\t\tif (contentRect) {\n\t\t\t\t\t\t// 总可滚动高度 = 内容高度 - 视口高度（或scroll-view自身高度）\n\t\t\t\t\t\t// 这里使用uni.getSystemInfoSync().windowHeight作为视口高度的参考\n\t\t\t\t\t\ttotalScrollHeight = contentRect.height - uni.getSystemInfoSync().windowHeight;\n\t\t\t\t\t\tif (totalScrollHeight < 0) totalScrollHeight = 0; // 防止负值\n\t\t\t\t\t}\n\t\t\t\t}).exec();\n\t\t\t}\n\t\t}).exec();\n\t});\n\t\n\tonPageScroll((e) => {\n\t\tif (totalScrollHeight > 0) {\n\t\t\treadingProgress.value = (e.scrollTop / totalScrollHeight) * 100;\n\t\t\tif (readingProgress.value > 100) readingProgress.value = 100; // 确保不超过100%\n\t\t} else {\n\t\t\treadingProgress.value = 0;\n\t\t}\n\t});\n\t\n\t// 滚动到顶部的方法\n\tconst scrollToTop = () => {\n\t\tuni.pageScrollTo({\n\t\t\tscrollTop: 0,\n\t\t\tduration: 300\n\t\t});\n\t};\n</script>\n\n\n<style lang=\"scss\" scoped>\n\t\n\t/* 返回顶部按钮和阅读进度样式 */\n\t.scroll-controls {\n\t\tposition: fixed;\n\t\tbottom: 130rpx; /* 距离底部 */\n\t\tright: 20rpx; /* 距离右侧 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tz-index: 99; /* 确保在内容之上，但在弹窗之下 */\n\t}\n\t\n\t.reading-progress {\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\n\t\tcolor: #fff;\n\t\tfont-size: 24rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 30rpx;\n\t\tmargin-bottom: 10rpx;\n\t\twhite-space: nowrap; /* 防止百分比换行 */\n\t}\n\t\n\t.back-to-top-button {\n\t\tbackground-color: #0f4c81; /* 匹配主题色 */\n\t\tcolor: #fff;\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%; /* 圆形按钮 */\n\t\tdisplay: flex; /* 使用flexbox来居中图片 */\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);\n\t\tborder: none;\n\t\tpadding: 0; /* 移除默认padding */\n\t} \n\t\n\t.top-arrow-icon {\n\t\twidth: 80rpx; /* 图片大小 */\n\t\theight: 80rpx;\n\t\t/* 可以添加滤镜来改变颜色，如果图片是黑色的 */\n\t\t/* filter: invert(100%); */\n\t}\n\t\n.container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh; \n  background-color: #f0f2f5; /* 更柔和的页面背景色 */\n}\n\n/* 顶部状态栏占位，用于适配刘海屏等设备 */\n.status-bar-placeholder {\n  height: var(--status-bar-height); /* uni-app 提供的状态栏高度变量 */\n  width: 100%;\n  background-color: #ffffff; /* 状态栏背景色 */\n}\n\n.main-scroll-view {\n  flex: 1; /* 占据剩余空间，允许内容滚动 */\n  box-sizing: border-box; /* 边框盒模型 */\n}\n\n.content-wrapper {\n  width: 100%;\n  max-width: 750px; /* 限制内容最大宽度，模拟原 HTML 的 750px */\n  margin: 0 auto; /* 居中显示 */\n  background-color: #ffffff; /* 内容区域背景色 */\n  padding: 20px; /* 增加内容区域内边距 */\n  box-sizing: border-box; /* 边框盒模型 */\n  font-family: -apple-system, BlinkMacSystemFont, \"PingFang SC\", \"Helvetica Neue\", \"Microsoft YaHei UI\", \"Microsoft YaHei\", Arial, sans-serif;\n  font-size: 16px;\n  line-height: 1.8; /* 增加行高，提升可读性 */\n  color: #333333; /* 默认文字颜色 */\n  border-radius: 12px; /* 添加圆角 */\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* 柔和的阴影 */\n}\n\n/* 欢迎标题样式 */\n.welcome-title-wrapper {\n  text-align: center;\n  margin: 1.5em auto 1.5em; /* 调整上下间距 */\n  padding: 0 1em 10px; /* 增加底部内边距 */\n  border-bottom: 3px solid #0F4C81; /* 稍微加粗底部边框 */\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: fit-content;\n  margin-top: 0;\n}\n\n.welcome-title {\n  font-size: 22px; /* 稍微增大字体 */\n  font-weight: bold;\n  line-height: 1.5; /* 调整行高 */\n  color: #2c3e50; /* 更深沉的颜色 */\n  white-space: nowrap;\n}\n\n/* 新同学问候样式 */\n.greeting-section {\n  padding-left: 12px; /* 增加左侧内边距 */\n  border-left: 4px solid #0F4C81; /* 稍微加粗左侧边框 */\n  margin: 2.5em 0 1.2em 0; /* 增加上下间距 */\n}\n\n.greeting-text {\n  font-size: 18px; /* 稍微增大字体 */\n  line-height: 1.3;\n  font-weight: bold;\n  color: #34495e; /* 更深沉的颜色 */\n}\n\n/* 各章节标题样式 */\n.section-title-wrapper {\n  text-align: center;\n  margin: 4em auto 2.5em; /* 增加上下间距 */\n  padding: 8px 15px; /* 增加内边距，形成“药丸”形状 */\n  background: linear-gradient(to right, #0F4C81, #2c7bb6); /* 渐变背景 */\n  border-radius: 25px; /* 圆角，形成“药丸”形状 */\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: fit-content;\n  box-shadow: 0 2px 8px rgba(15, 76, 129, 0.3); /* 添加阴影 */\n}\n\n.section-title {\n  font-size: 20px; /* 稍微增大字体 */\n  font-weight: bold;\n  line-height: 1.5;\n  color: #fff;\n  white-space: nowrap;\n}\n\n/* 普通信息段落样式 */\n.info-paragraph {\n  margin: 1.8em 0; /* 调整上下间距，左右由 content-wrapper 决定 */\n  letter-spacing: 0.05em; /* 稍微调整字间距 */\n  line-height: 1.8; /* 统一行高 */\n}\n\n/* 列表信息样式 */\n.info-list {\n  padding-left: 0; /* 移除 ul/ol 默认的左边距 */\n  margin-left: 0;\n  line-height: 1.8; /* 统一行高 */\n}\n\n.list-item {\n  display: flex;\n  margin-bottom: 0.8em; /* 增加列表项之间的间距 */\n  align-items: flex-start;\n}\n\n.list-item-number {\n  font-size: 16px;\n  line-height: 1.8;\n  margin-right: 0.6em; /* 调整数字和内容之间的间距 */\n  flex-shrink: 0;\n  font-weight: bold; /* 数字加粗 */\n  color: #0F4C81; /* 数字颜色 */\n}\n\n.list-item-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 粗体文本样式 */\n.info-strong {\n  font-weight: bold;\n  color: #0F4C81; /* 保持原蓝色 */\n}\n\n/* 普通文本样式 */\n.info-text {\n  font-size: 16px;\n  color: #333333;\n  line-height: 1.8;\n  letter-spacing: 0.05em;\n  display: block;\n}\n\n/* 蓝色粗体文本样式 */\n.info-strong-blue {\n  font-weight: bold;\n  color: #0F4C81;\n}\n\n/* 自定义换行符样式，用于模拟 <br/> */\n.newline {\n  display: block;\n  height: 0.5em; /* 控制换行间距 */\n  content: '';\n}\n\n/* 底部小字提示 */\n.small-text {\n  font-size: 13px; /* 适当缩小字体 */\n  color: #7f8c8d; /* 更柔和的灰色 */\n  margin-top: 2em; /* 增加与上方内容的间距 */\n  line-height: 1.6;\n  display: block;\n  text-align: justify; /* 两端对齐 */\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/xuchang/xuchang.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onReady", "uni", "onPageScroll"], "mappings": ";;;;;AAwMA,UAAA,kBAAAA,kBAAA,CAAA;AAMA,QAAA,oBAAA;AAEAC,kBAAAA,QAAA,MAAA;AAEAC,oBAAA,MAAA,oBAAA,EAAA,OAAA,mBAAA,EAAA,mBAAA,UAAA;AACA,YAAA,MAAA;AAIAA,wBAAA,MAAA,oBAAA,EAAA,OAAA,kBAAA,EAAA,mBAAA,iBAAA;AACA,gBAAA,aAAA;AAGA,kCAAA,YAAA,SAAAA,cAAA,MAAA,kBAAA,EAAA;AACA,kBAAA,oBAAA;AAAA,oCAAA;AAAA,YACA;AAAA,UACA,CAAA,EAAA,KAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,KAAA;AAAA,IACA,CAAA;AAEAC,kBAAA,aAAA,CAAA,MAAA;AACA,UAAA,oBAAA,GAAA;AACA,wBAAA,QAAA,EAAA,YAAA,oBAAA;AACA,YAAA,gBAAA,QAAA;AAAA,0BAAA,QAAA;AAAA,MACA,OAAA;AACA,wBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,cAAA,MAAA;AACAD,oBAAAA,MAAA,aAAA;AAAA,QACA,WAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;ACjPA,GAAG,WAAW,eAAe;"}