"use strict";const e=require("../../../common/vendor.js"),n={__name:"changyong",setup(n){const t=e.ref([{src:"https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南"}]);e.onShareAppMessage((()=>({title:"河南农业大学新生指南中心",path:"/pages/index/index",imageUrl:t.value[0].src})));const a=n=>{const t=encodeURIComponent(n);e.index.navigateTo({url:`/pages/NewStudentKnowledge/web-view/web-view?url=${t}`})};return(n,t)=>({a:e.o((e=>a("https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE"))),b:e.o((e=>a("https://cwwx.henau.edu.cn/xysf/aAppPage/index.aspx?mac=70f02c7dd15ac82d13b3550bdf939810#/loginTemp/loginIng")))})}},t=e._export_sfc(n,[["__scopeId","data-v-4a0c4531"]]);n.__runtimeHooks=2,wx.createPage(t);
