/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-5c9efd14 {
  padding: 30rpx;
  background-color: #f0f4f7;
  min-height: 100vh;
}
.header.data-v-5c9efd14 {
  text-align: center;
  margin-bottom: 40rpx;
}
.header-title.data-v-5c9efd14 {
  font-size: 40rpx;
  font-weight: bold;
  color: #2c3e50;
}
.card-section.data-v-5c9efd14 {
  margin-bottom: 40rpx;
}
.card.data-v-5c9efd14 {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}
.card.data-v-5c9efd14:active {
  transform: translateY(2rpx);
}
.card-title-container.data-v-5c9efd14 {
  position: relative;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}
.card-title-container.data-v-5c9efd14::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 10rpx;
  height: 90%;
  background-color: #6699CC;
  border-radius: 5rpx;
}
.card-title.data-v-5c9efd14 {
  font-size: 36rpx;
  font-weight: bold;
  color: #34495e;
  margin-left: 10rpx;
}
.text.data-v-5c9efd14 {
  color: #55aa00;
}
.list-item.data-v-5c9efd14 {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}
.list-item.data-v-5c9efd14:last-child {
  border-bottom: none;
}
.item-header.data-v-5c9efd14 {
  display: flex;
  align-items: center;
  margin-bottom: 5rpx;
}
.item-icon.data-v-5c9efd14 {
  border-radius: 10px;
  width: 48rpx;
  height: 48rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.item-title.data-v-5c9efd14 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
  flex: 1;
}
.item-desc.data-v-5c9efd14 {
  font-size: 28rpx;
  color: #888;
  line-height: 1.5;
  margin-top: 5rpx;
  padding-left: 63rpx;
  /* 适配图标宽度 + margin-right，使描述文本对齐标题 */
}
.platform-item.data-v-5c9efd14 {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}
.platform-item.data-v-5c9efd14:last-child {
  border-bottom: none;
}
.link-group.data-v-5c9efd14 {
  margin-top: 10rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.link-button.data-v-5c9efd14 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0f0ff;
  color: #4a77a8;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 16rpx;
  margin-top: 10rpx;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(74, 119, 168, 0.2);
}
.link-button.data-v-5c9efd14:active {
  background-color: #cce0ff;
  transform: scale(0.97) translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.15);
}
.note-text.data-v-5c9efd14 {
  font-size: 24rpx;
  color: #777;
  margin-top: 10rpx;
}