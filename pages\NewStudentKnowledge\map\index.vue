<template>
	<view class="page-container">
		<view class="content-wrapper">
			<view class="header-container">
				<text class="header-title">校区地图导航</text>
				<text class="header-subtitle">探索河南农业大学的三大校区</text>
				<text class="sub-text">点击图片可查看详细地图</text>
			</view>

			<view class="images-display-container">
				<view v-for="(item, index) in images" :key="index" class="campus-image-item" @tap="previewImage(item.src)">
					<view class="image-wrapper">
						<image :src="item.src" mode="aspectFill" class="campus-image" />
					</view>
					<view class="campus-info">
						<text class="campus-name">{{ item.name }}</text>
						<text class="campus-desc">{{ item.description }}</text>
					</view>
				</view>
			</view>

			<view class="buttons-container">
				<button class="action-button map-button" @click="copy3DMapLink">
					<text class="iconfont icon-3d"></text>
					<text class="button-text">龙子湖校区3D导览</text>
				</button>
				<button class="action-button photo-button" @click="navigateToLandscape">
					<text class="iconfont icon-photo"></text>
					<text class="button-text">农大风景照片</text>
				</button>
			</view>
		</view>

		<!-- 自定义弹窗逻辑保持不变 -->
		<view class="custom-modal-overlay" v-if="showCustomModal" @tap="closeCustomModal">
			<view class="custom-modal-content" @tap.stop>
				<text class="modal-title">温馨提示</text>
				<text class="modal-message">3D地图链接已复制到剪贴板。由于微信环境限制，请粘贴到浏览器中查看完整地图。</text>
				<button class="modal-button" @tap="closeCustomModal">知道了</button>
			</view>
		</view>
		<view class="bottom">
			<text>注:图源于 共青团河南农业大学委员会微晓工作室</text>
			<br />
			<text>3D校园源于河南农业大学信管学院</text>
		</view>
		<view class="bottom">
			技术支持 河南农业大学IT工作室
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';

const images = ref([
	{
		src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg',
		name: '许昌校区',
		description: '新建校区，现代化教学设施',
	},
	{
		src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg',
		name: '龙子湖校区',
		description: '主校区，位于郑州市龙子湖高校园区',
	},
	{
		src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png',
		name: '文化路校区',
		description: '百年老校区，省级文物保护单位',
	}
]);

// 图片预览逻辑已修改为跳转到详情页
const previewImage = (imageUrl) => {
	uni.navigateTo({
		url: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`,
	});
};

// 自定义弹窗逻辑
const showCustomModal = ref(false);

const closeCustomModal = () => {
	showCustomModal.value = false;
};

// 3D地图链接复制逻辑
const copy3DMapLink = () => {
	const mapUrl = 'https://www.720yun.com/vr/315z05drknk?s=332068';

	uni.setClipboardData({
		data: mapUrl,
		success: function () {
			showCustomModal.value = true;
		},
		fail: function () {
			uni.showToast({
				title: '复制失败，请手动复制',
				icon: 'none',
			});
		},
	});
};

// 新增的跳转函数
const navigateToLandscape = () => {
	uni.navigateTo({
		url: '/pages/NewStudentKnowledge/landscape/landscape',
	});
};
</script>

<style lang="scss" scoped>
// 基础样式优化
.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
	padding: 30rpx;
	box-sizing: border-box;
}

// 内容区域优化
.content-wrapper {
	// 移除背景色，只保留阴影
	background: transparent;
	border-radius: 30rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
	padding: 40rpx;
	backdrop-filter: blur(20rpx);
}

// 标题样式优化
.header-container {
	text-align: center;
	margin-bottom: 60rpx;

	.header-title {
		font-size: 48rpx;
		font-weight: 800;
		// 渐变文字效果
		background: linear-gradient(45deg, #2c5364, #203a43);
		-webkit-background-clip: text;
		color: transparent;
		margin-bottom: 20rpx;
		display: block;
		// 增加描边，让标题更突出
		-webkit-text-stroke: 1rpx #fff;
		text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.header-subtitle {
		font-size: 32rpx;
		color: #000000; // 调整为黑色
		margin-bottom: 10rpx;
		display: block;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.sub-text {
		font-size: 24rpx;
		color: #555500; // 调整为浅色
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
}

// 图片卡片样式优化
.images-display-container {
	margin-top: 40rpx;
}

.campus-image-item {
	margin-bottom: 40rpx;
	border-radius: 20rpx;
	overflow: hidden;
	// 移除白色背景，使用半透明深色背景
	background: rgba(0, 0, 0, 0.1);
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		transform: translateY(-6rpx);
		box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
	}

	.image-wrapper {
		position: relative;
		overflow: hidden;

		.campus-image {
			width: 100%;
			height: 360rpx;
			transition: transform 0.3s ease;
		}
	}

	.campus-info {
		// 使用半透明深色背景
		background: rgba(0, 0, 0, 0.5);
		padding: 30rpx;
		backdrop-filter: blur(5rpx); // 增加毛玻璃效果

		.campus-name {
			font-size: 36rpx;
			font-weight: 600;
			color: #fff; // 调整为白色
			margin-bottom: 10rpx;
			display: block;
		}

		.campus-desc {
			font-size: 28rpx;
			color: #ddd; // 调整为浅色
			line-height: 1.5;
			display: block;
		}
	}
}

// 按钮样式优化
.buttons-container {
	margin-top: 40rpx;
	display: flex;
	justify-content: center;
	gap: 30rpx; // 按钮之间的间距
	flex-wrap: wrap; // 允许按钮换行
}

.action-button {
	// 公共样式
	padding: 25rpx 30rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	border: none;
	&:after {
		border: none;
	}
	.button-text {
		font-size: 22rpx;
		font-weight: 600;
		color: #fff;
	}
	&:active {
		transform: scale(0.98);
	}
}

.map-button {
	background: linear-gradient(45deg, #00b09b, #96c93d);
}

.photo-button {
	background: linear-gradient(45deg, #ff6b6b, #f06595);
}

// 响应式布局优化
@media screen and (min-width: 768px) {
	.images-display-container {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 30rpx;
	}
	.campus-image-item {
		margin-bottom: 0;
	}
}

/* 自定义美化弹窗样式 */
.custom-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: fadeIn 0.3s ease-out;
}

.custom-modal-content {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 50rpx;
	margin: 40rpx;
	max-width: 600rpx;
	width: 90%;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	animation: slideIn 0.3s ease-out;
}

.modal-title {
	font-size: 38rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 25rpx;
}

.modal-message {
	font-size: 30rpx;
	color: #555;
	line-height: 1.6;
	margin-bottom: 40rpx;
}

.modal-button {
	background: linear-gradient(45deg, #4a90ee, #62b0ff);
	color: #fff;
	border-radius: 40rpx;
	font-size: 22rpx;
	height: 70rpx;
	width: 80%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 15rpx rgba(74, 144, 238, 0.3);
	transition: all 0.2s ease-in-out;
	border: none;
	&:after {
		border: none;
	}
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 3rpx 8rpx rgba(74, 144, 238, 0.5);
	}
}

/* 动画效果 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}










.bottom{
	margin-top: 10px;
	font-size: 10px;
	color: #555;
	font-weight: 10px;
	text-align: center;
	}
	
	
	
	
	
	
	
	
	
	
	
	


@keyframes slideIn {
	from {
		transform: translateY(-50px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
</style>
