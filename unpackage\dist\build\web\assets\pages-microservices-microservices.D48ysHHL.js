import{c as e,w as s,j as a,o,a as r,k as n,l as t,m as c}from"./index-DeHPfleJ.js";import{_ as i}from"./home.LEWUQR7v.js";import{_ as l,o as p,a as d}from"./_plugin-vue_export-helper.DFaavnJe.js";const m=l({__name:"microservices",setup:l=>(p((e=>{const s=e.webViewUrl;return{path:`/pages/index/index?webViewUrl=${encodeURIComponent(s)}`}})),d((()=>{})),(l,p)=>{const d=t,m=c,u=a;return o(),e(u,{src:"https://microservice.leesong.top/henauwfw/#/index"},{default:s((()=>[r(m,{class:"close-view",onClick:p[0]||(p[0]=e=>{n({url:"/pages/index/index"})})},{default:s((()=>[r(d,{class:"close-icon",src:i})])),_:1})])),_:1})})},[["__scopeId","data-v-568c3489"]]);export{m as default};
