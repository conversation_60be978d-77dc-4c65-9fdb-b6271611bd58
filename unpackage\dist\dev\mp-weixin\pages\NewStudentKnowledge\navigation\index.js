"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    // 导航到指定校区的方法
    navigateTo(campus) {
      common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/navigation/index.vue:100", `导航到 ${campus} 校区`);
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/${campus}/${campus}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(($event) => $options.navigateTo("wenhua")),
    b: common_vendor.o(($event) => $options.navigateTo("longzihu")),
    c: common_vendor.o(($event) => _ctx.jumpxuchang()),
    d: common_vendor.o(($event) => $options.navigateTo("xuchang")),
    e: common_vendor.o(($event) => $options.navigateTo("changyong"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-17384c66"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/navigation/index.js.map
