"use strict";const e=require("../../../common/vendor.js"),o={__name:"web-view",setup(o){const n=e.ref(""),r=e.ref(!0);e.onLoad((o=>{o.url?(n.value=decodeURIComponent(o.url),console.log("加载外部链接:",n.value)):(console.error("未接收到有效的URL参数"),e.index.showToast({title:"链接无效",icon:"none"}),r.value=!1)}));const t=()=>{console.log("Webview加载成功"),r.value=!1},a=o=>{console.error("Webview加载失败:",o.detail),e.index.showToast({title:"链接加载失败，请检查网络或稍后重试",icon:"none",duration:3e3}),r.value=!1};return(o,r)=>({a:n.value,b:e.o(t),c:e.o(a)})}},n=e._export_sfc(o,[["__scopeId","data-v-2872bd9b"]]);wx.createPage(n);
