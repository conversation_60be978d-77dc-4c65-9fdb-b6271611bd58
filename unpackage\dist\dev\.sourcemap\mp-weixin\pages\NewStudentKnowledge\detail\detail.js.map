{"version": 3, "file": "detail.js", "sources": ["pages/NewStudentKnowledge/detail/detail.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9kZXRhaWwvZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"detail-page\" @tap=\"closeDetailPage\">\n\t\t<movable-area class=\"movable-area\">\n\t\t\t<movable-view\n\t\t\t\tclass=\"movable-view\"\n\t\t\t\tdirection=\"all\"\n\t\t\t\tscale\n\t\t\t\tscale-min=\"1\"\n\t\t\t\tscale-max=\"3\"\n\t\t\t\t:scale-value=\"scaleValue\"\n\t\t\t\tout-of-bounds\n\t\t\t\**********=\"resetScaleAndClose\"\n\t\t\t>\n\t\t\t\t<image :src=\"imageUrl\" mode=\"widthFix\" class=\"full-image\" @load=\"onImageLoad\"></image>\n\t\t\t</movable-view>\n\t\t</movable-area>\n\t\t<text class=\"close-tip\">双指可缩放图片，单击任意处返回</text>\n\t</view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\n\nconst imageUrl = ref('');\nconst scaleValue = ref(1); // 用于控制图片缩放的初始值\n\n// 在页面加载时获取传递过来的参数\nonLoad((options) => {\n\tif (options.imageUrl) {\n\t\timageUrl.value = decodeURIComponent(options.imageUrl);\n\t}\n});\n\n// 图片加载完成时重置缩放值，确保每次进入页面都是初始大小\nconst onImageLoad = () => {\n\tscaleValue.value = 1;\n};\n\n// 点击 movable-view 或背景时关闭页面\nconst closeDetailPage = () => {\n\tuni.navigateBack();\n};\n\n// 点击图片时重置缩放并关闭页面\nconst resetScaleAndClose = () => {\n\tscaleValue.value = 1; // 确保在关闭前重置缩放，避免下次进入时仍是放大状态\n\tuni.navigateBack();\n};\n</script>\n\n<style scoped>\n.detail-page {\n\tdisplay: flex;\n\tflex-direction: column; /* 调整为列布局，以便提示文字在底部 */\n\tjustify-content: center;\n\talign-items: center;\n\twidth: 100vw;\n\theight: 100vh;\n\tbackground-color: #000; /* 黑色背景，更好地展示图片 */\n\tposition: fixed; /* 固定定位，确保覆盖整个屏幕 */\n\ttop: 0;\n\tleft: 0;\n\tz-index: 9999; /* 确保在最上层 */\n\toverflow: hidden; /* 防止页面滚动 */\n}\n\n.movable-area {\n\twidth: 100%;\n\theight: 100%;\n\t/* 确保 movable-area 能够覆盖整个可见区域，以便拖动和缩放 */\n}\n\n.movable-view {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\t/* movable-view 的大小通常设置为 100% 100% */\n}\n\n.full-image {\n\twidth: 100%; /* 图片宽度填充 movable-view */\n\theight: auto; /* 高度自适应，保持图片比例 */\n\t/* mode=\"widthFix\" 确保图片宽度固定，高度自适应 */\n}\n\n.close-tip {\n\tposition: absolute;\n\tbottom: 40rpx;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tcolor: rgba(255, 255, 255, 0.8);\n\tfont-size: 24rpx;\n\tpadding: 10rpx 30rpx;\n\tborder-radius: 30rpx;\n\tbackground: rgba(0, 0, 0, 0.3);\n\twhite-space: nowrap;\n\tz-index: 10000; /* 确保提示文字在最上层 */\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/detail/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni"], "mappings": ";;;;;AAwBA,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxBC,kBAAM,OAAC,CAAC,YAAY;AACnB,UAAI,QAAQ,UAAU;AACrB,iBAAS,QAAQ,mBAAmB,QAAQ,QAAQ;AAAA,MACpD;AAAA,IACF,CAAC;AAGD,UAAM,cAAc,MAAM;AACzB,iBAAW,QAAQ;AAAA,IACpB;AAGA,UAAM,kBAAkB,MAAM;AAC7BC,oBAAG,MAAC,aAAY;AAAA,IACjB;AAGA,UAAM,qBAAqB,MAAM;AAChC,iBAAW,QAAQ;AACnBA,oBAAG,MAAC,aAAY;AAAA,IACjB;;;;;;;;;;;;;AC/CA,GAAG,WAAW,eAAe;"}