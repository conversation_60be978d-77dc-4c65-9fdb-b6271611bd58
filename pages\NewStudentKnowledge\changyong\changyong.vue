<template>
  <view class="page-container">
    <view class="header">
      <text class="header-title">常用软件与平台</text>
    </view>

    <view class="card-section">
      <view class="card">
        <view class="card-title-container">
          <text class="card-title">常用平台</text>
        </view>

        <view class="platform-item">
          <text class="item-title">河南农业大学专属学习交流平台</text>
          <view class="link-group">
            <!-- 农宝圈链接，通过navigateToWebview方法跳转到web-view页面 -->
            <view class="link-button" @click="navigateToWebview('https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE')">
              <text>农宝圈</text>
            </view>
          </view>
        </view>

        <view class="platform-item">
          <text class="item-title">学生卡充值、电费充值</text>
          <!-- 学生卡充值链接，通过navigateToWebview方法跳转到web-view页面 -->
          <view>
            <text class="text">请搜索"河南农业大学信息化办公室"公众号点击"校园卡"选项</text>
          </view>
        </view>

        <view class="platform-item">
          <text class="item-title">学费缴纳</text>
          <!-- 学费缴纳链接，通过navigateToWebview方法跳转到web-view页面 -->
          <view class="link-button" @click="navigateToWebview('https://cwwx.henau.edu.cn/xysf/aAppPage/index.aspx?mac=70f02c7dd15ac82d13b3550bdf939810#/loginTemp/loginIng')">
            <text>河南农业大学财务处</text>
          </view>
          <text class="note-text">（用户名为学号，密码默认为身份证后六位）</text>
        </view>
      </view>
    </view>

    <view class="card-section">
      <view class="card">
        <view class="card-title-container">
          <text class="card-title">常用软件</text>
        </view>
        <!-- 喜鹊儿图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbcfaa122_1754577871.png" mode="aspectFit"></image>
            <text class="item-title">喜鹊儿</text>
          </view>
          <text class="item-desc">查课表、查成绩、选课、申请调课等</text>
        </view>
        <!-- 学习通图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc14ee537_1754577940.png" mode="aspectFit"></image>
            <text class="item-title">学习通</text>
          </view>
          <text class="item-desc">刷网课、提交作业</text>
        </view>
        <!-- 大学生MOOC图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd621ad7_1754577878.png" mode="aspectFit"></image>
            <text class="item-title">大学生MOOC</text>
          </view>
          <text class="item-desc">刷网课、提交作业</text>
        </view>
        <view class="list-item">
          <view class="item-header">
    
			   <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc7a903c2_1754578042.png" mode="aspectFit"></image>
            <text class="item-title">WE Learn ,词达人</text>
          </view>
          <text class="item-desc">大英刷课用</text>
        </view>
        <!-- 胖乖生活图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc51c2a4a_1754578001.png" mode="aspectFit"></image>
            <text class="item-title">胖乖生活(许昌，龙子湖使用)</text>
          </view>
          <text class="item-desc">学校澡堂洗澡要用</text>
        </view>
        <view class="list-item">
          <view class="item-header">
           			   <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd142a34_1754577873.png" mode="aspectFit"></image>
            <text class="item-title">大白U帮(桃李园用)</text>
          </view>
          <text class="item-desc">学校澡堂洗澡要用</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {
  onShareAppMessage
} from '@dcloudio/uni-app';
import {
  ref
} from 'vue';

// 假设有一个bannerList用于分享图片，这里提供一个默认值
const bannerList = ref([{
  src: 'https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南'
}]); // 请替换为实际的图片URL

// 微信小程序分享配置
// 当用户点击右上角菜单中的“转发”按钮时，会调用此函数
onShareAppMessage(() => {
  return {
    title: '河南农业大学新生指南中心', // 分享卡的标题
    path: '/pages/index/index', // 分享后用户点击进入的页面路径，请确保路径正确
    imageUrl: bannerList.value[0].src, // 分享卡上显示的图片，这里使用第一张轮播图
  };
});

/**
 * 导航到web-view页面，用于加载外部链接
 * @param {string} externalUrl 外部链接URL
 */
const navigateToWebview = (externalUrl) => {
  // 对URL进行编码，以便在跳转时作为参数传递
  const encodedUrl = encodeURIComponent(externalUrl);
  // *** 关键修改：将路径从 /pages/webview/webview 改为 /pages/web-view/web-view ***
  uni.navigateTo({
    url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`
  });
};
</script>

<style lang="scss" scoped>
// 使用scss可以更好地组织和管理样式
.page-container {
  padding: 30rpx;
  background-color: #f0f4f7; // 页面背景色调整为更柔和的浅蓝色
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2c3e50; // 深灰色标题
}

.card-section {
  margin-bottom: 40rpx;
}

.card {
  background-color: #fff;
  border-radius: 24rpx; // 边角更圆润
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08); // 更明显的阴影效果
  transition: transform 0.3s ease; // 添加过渡效果
  &:active {
    transform: translateY(2rpx); // 点击时轻微下沉
  }
}

.card-title-container {
  position: relative;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.card-title-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 10rpx; // 强调条更宽
  height: 90%; // 强调条更高
  background-color: #6699CC; // 柔和的蓝色强调条
  border-radius: 5rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #34495e; // 标题颜色
  margin-left: 10rpx; // 标题与强调条间距
}
.text{
	color: #55aa00;
}

.list-item {
  display: flex;
  flex-direction: column; // 保持主轴为列，标题和描述上下排列
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef; // 更细的分割线
  &:last-child {
    border-bottom: none;
  }
}

.item-header {
  display: flex; // 内部使用flex布局，让图标和标题并排
  align-items: center; // 垂直居中对齐
  margin-bottom: 5rpx; // 标题和描述之间留一点间距
}

.item-icon {
	border-radius: 10px;
  width: 48rpx; // 图标大小
  height: 48rpx; // 图标大小
  margin-right: 15rpx; // 图标与文字的间距
  flex-shrink: 0; // 防止图标被压缩
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
  flex: 1; // 标题占据剩余空间
}

.item-desc {
  font-size: 28rpx;
  color: #888; // 描述文字颜色更柔和
  line-height: 1.5;
  margin-top: 5rpx;
  padding-left: 63rpx; /* 适配图标宽度 + margin-right，使描述文本对齐标题 */
}

.platform-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
  &:last-child {
    border-bottom: none;
  }
}

.link-group {
  margin-top: 10rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.link-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0f0ff; // 浅蓝色背景
  color: #4a77a8; // 链接文字颜色
  font-size: 28rpx;
  padding: 16rpx 30rpx; // 按钮更大，更饱满
  border-radius: 16rpx; // 按钮边角更圆润
  margin-top: 10rpx;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease; // 添加过渡效果
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1); // 按钮阴影
  border: 1rpx solid rgba(74, 119, 168, 0.2); // 浅色边框
  &:active {
    background-color: #cce0ff; // 点击时变深
    transform: scale(0.97) translateY(2rpx); // 点击时轻微缩小并下沉
    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.15); // 点击时阴影变小
  }
}

.note-text {
  font-size: 24rpx;
  color: #777; // 备注文字颜色
  margin-top: 10rpx;
}
</style>
