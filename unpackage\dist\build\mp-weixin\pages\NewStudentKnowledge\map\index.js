"use strict";const e=require("../../../common/vendor.js"),n={__name:"index",setup(n){const t=e.ref([{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg",name:"许昌校区",description:"新建校区，现代化教学设施"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg",name:"龙子湖校区",description:"主校区，位于郑州市龙子湖高校园区"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png",name:"文化路校区",description:"百年老校区，省级文物保护单位"}]),a=e.ref(!1),o=()=>{a.value=!1},d=()=>{e.index.setClipboardData({data:"https://www.720yun.com/vr/315z05drknk?s=332068",success:function(){a.value=!0},fail:function(){e.index.showToast({title:"复制失败，请手动复制",icon:"none"})}})},s=()=>{e.index.navigateTo({url:"/pages/NewStudentKnowledge/landscape/landscape"})};return(n,i)=>e.e({a:e.f(t.value,((n,t,a)=>({a:n.src,b:e.t(n.name),c:e.t(n.description),d:t,e:e.o((t=>{return a=n.src,void e.index.navigateTo({url:`/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(a)}`});var a}),t)}))),b:e.o(d),c:e.o(s),d:a.value},a.value?{e:e.o(o),f:e.o((()=>{})),g:e.o(o)}:{})}},t=e._export_sfc(n,[["__scopeId","data-v-188de0ff"]]);wx.createPage(t);
