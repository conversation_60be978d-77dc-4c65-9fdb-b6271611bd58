<template>
    <view class="page-container" @tap="handlePageTap">

        <view class="top">
            <text class="top-title">河南农业大学</text>
            <text class="top-subtitle">新生指南中心</text>
        </view>

        <!-- 返回按钮 -->
        <view class="back-button" @tap.stop="goBack">
            <!-- 使用 image 标签来显示图标 -->
            <image class="back-icon-img" src="https://itstudio.henau.edu.cn/image_hosting/uploads/68960239b210b_1754661433.png" alt="返回"></image>
        </view>

        <!-- 轮播图组件 -->
        <swiper
            :indicator-dots="true"
            :autoplay="true"
            :interval="3000"
            :duration="1000"
            :circular="true"
            indicator-active-color="#07c160"
            class="banner-swiper"
            @change="handleSwiperChange"
        >
            <swiper-item class="swiper-item" v-for="(item, index) in bannerList" :key="index">
                <image
                    class="banner-image"
                    :src="item.src"
                    mode="aspectFill"
                    @error="handleImageError(index)"
                ></image>
            </swiper-item>
        </swiper>

        <!-- 引言部分 -->
        <view class="intro-box" @tap="handleIntroClick">
            <text class="intro-title">引言</text>
            <view class="intro-content">
                <text class="intro-text">{{ introText }}</text>
                <!-- 打字机效果光标 -->
                <text v-if="!introTypingDone" class="typing-cursor">|</text>
            </view>
        </view>

        <!-- 烟花特效蒙版 -->
        <view v-if="showFirework" class="firework-overlay" @tap="hideFirework">
            <view class="firework-container">
                <view
                    v-for="(firework, index) in fireworks"
                    :key="index"
                    class="firework-particle"
                    :style="firework.style"
                ></view>
            </view>
            <text class="firework-text">🎉 欢迎开启新的人生篇章！🎉</text>
        </view>

        <!-- 统计信息弹窗 -->
        <view v-if="showStatsModal" class="stats-modal-overlay" @tap="showStatsModal = false">
            <view class="stats-modal-content" @tap.stop>
                <text class="modal-title">用户访问统计</text>
                <view v-if="statsData.summary" class="stats-section">
                    <text class="stats-item">总用户数: {{ statsData.summary.totalUsers }}</text>
                    <text class="stats-item">活跃用户: {{ statsData.summary.activeUsers }}</text>
                    <text class="stats-item">新用户: {{ statsData.summary.newUsers }}</text>
                    <text class="stats-item">总访问量: {{ statsData.summary.totalVisits }}</text>
                </view>
                <text v-else class="stats-item">加载统计数据中...</text>
                <text class="close-modal-btn" @tap="showStatsModal = false">关闭</text>
            </view>
        </view>
		<!-- 技术支持部分和访问量，只有在打字机效果完成后才显示 -->
		<view class="bottom" v-if="introTypingDone">
			<text>技术支持 河南农业大学IT工作室</text>
			<text v-if="statsData.summary"> | 访问量: {{ statsData.summary.totalVisits }}</text>
		</view>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app';

// 登陆的baseUrl
const backUrl = 'http://localhost';

// 轮播图数据
const bannerList = ref([
    { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/68931460c8aeb_1754469472.png', alt: '校园风光' },
    { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893147e19cdd_1754469502.png', alt: '新生报到' },
    { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893149faf7f0_1754469535.png', alt: '教学活动' },
]);

// 引言打字机效果相关数据
const introText = ref('');
const fullIntroText = '踏入新征程，开启新篇章，这里是你梦想的起点！在这里，你将收获知识的力量，结识志同道合的朋友，探索无限可能的未来。河南农业大学将为你提供广阔的学习平台，丰富的实践机会，优秀的师资团队，完善的校园设施。无论你来自哪里，无论你的专业是什么，这里都将成为你人生中最重要的成长阶段。让我们一起在这片充满希望的土地上，书写属于你的青春华章，追求学术的真理，培养创新的思维，锻炼实践的能力，成就更好的自己！愿你在这里度过充实而美好的大学时光，收获知识、友谊和成长！';
const introTypingDone = ref(false);
const introIntervalId = ref(null); // 用于存储打字机效果的定时器ID

// 烟花特效相关数据
const showFirework = ref(false);
const fireworks = ref([]);

// 统计信息弹窗相关数据
const showStatsModal = ref(false);
const statsData = ref({});
const introClickCount = ref(0); // 用于跟踪引言部分的点击次数

// 开始引言打字机动画
const startIntroTyping = () => {
    let i = 0;
    // 清除任何可能存在的旧定时器，防止重复
    if (introIntervalId.value) {
        clearInterval(introIntervalId.value);
    }
    introIntervalId.value = setInterval(() => {
        if (i < fullIntroText.length) {
            introText.value += fullIntroText[i];
            i++;
        } else {
            clearInterval(introIntervalId.value);
            introTypingDone.value = true;
            introIntervalId.value = null; // 动画完成后清除ID
        }
    }, 25);
};

// 轮播图切换事件处理
const handleSwiperChange = (e) => {
    // console.log('当前轮播索引：', e.detail.current);
};

// 图片加载错误处理，显示默认图片
const handleImageError = (index) => {
    bannerList.value[index].src = 'https://itstudio.henau.edu.cn/image_hosting/uploads/689314c7a5ee4_1754469575.png'; // 替换为默认图片
};

// 微信小程序分享配置
// 当用户点击右上角菜单中的“转发”按钮时，会调用此函数
onShareAppMessage(() => {
    return {
        title: '河南农业大学新生指南中心', // 分享卡的标题
        path: '/pages/index/index', // 分享后用户点击进入的页面路径，请确保路径正确
        imageUrl: bannerList.value[0].src, // 分享卡上显示的图片，这里使用第一张轮播图
    };
});


// 处理小程序登录逻辑
const handleLogin = () => {
    uni.login({
        provider: 'weixin', // 指定微信登录
        success(res) {
            if (res.code) {
                // 发送 code 到后端服务器进行验证和登录
                uni.request({
                    url: `${backUrl}:3000/api/auth/login`, // 您的后端登录接口
                    method: 'POST',
                    data: {
                        code: res.code,
                    },
                    success(res) {
                        console.log('登录成功！', res.data);
                    },
                    fail(err) {
                        console.error('登录请求失败：', err);
                    }
                });
            } else {
                console.log('uni.login 失败！' + res.errMsg);
            }
        },
        fail(err) {
            console.error('uni.login 调用失败：', err);
        }
    });
};

// 获取用户访问统计数据
const fetchUserStatistics = () => {
    uni.request({
        url: `${backUrl}:3000/api/users/statistics`,
        method: 'GET', // 根据API文档，这里应该是GET请求
        success(res) {
            if (res.statusCode === 200 && res.data.status === 'success') {
                statsData.value = res.data.data;
                showStatsModal.value = true; // 显示弹窗
                console.log('用户统计数据：', statsData.value);
            } else {
                console.error('获取用户统计数据失败：', res.data.message || '未知错误');
                uni.showToast({
                    title: '获取统计数据失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        fail(err) {
            console.error('请求用户统计数据失败：', err);
            uni.showToast({
                title: '网络错误，无法获取统计数据',
                icon: 'none',
                duration: 2000
            });
        }
    });
};

// 处理引言部分点击事件 (用于触发统计弹窗)
const handleIntroClick = () => {
    introClickCount.value++;
    console.log(`引言部分被点击了 ${introClickCount.value} 次`);
    if (introClickCount.value >= 3) {
        fetchUserStatistics();
        introClickCount.value = 0; // 重置点击计数器
    }
};

// 处理页面全局点击事件 (用于停止打字机效果)
const handlePageTap = () => {
    // 如果打字机效果仍在进行中且定时器存在
    if (!introTypingDone.value && introIntervalId.value !== null) {
        clearInterval(introIntervalId.value); // 停止打字计时器
        introText.value = fullIntroText;     // 立即显示完整文本
        introTypingDone.value = true;        // 标记打字机效果完成
        introIntervalId.value = null;        // 清空定时器ID
    }
};

// 返回上一页
const goBack = () => {
	uni.navigateTo({
		url:'/pages/index/index'
	})
  
};


// 页面加载时执行
onLoad(() => {
    console.log('页面加载');
    // 页面加载时开始引言打字机动画
    startIntroTyping();
    // 页面加载时执行登录请求
    handleLogin();
});

// 页面显示时执行
onShow(() => {
    console.log('页面显示');
});
</script>

<style lang="scss">
    .page-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
        padding: 15rpx;
        padding-top: calc(15rpx + env(safe-area-inset-top));
        padding-top: calc(15rpx + constant(safe-area-inset-top));
        padding-bottom: calc(15rpx + env(safe-area-inset-bottom));
        padding-bottom: calc(15rpx + constant(safe-area-inset-bottom));
        box-sizing: border-box;
        position: relative;
    }


/* 返回按钮样式 */
.back-button {
    position: absolute;
    top: calc(55rpx + env(safe-area-inset-top));
    left: 30rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    overflow: hidden; /* Ensure image doesn't overflow rounded button */
}

.back-button:active {
    transform: scale(0.9);
    background: rgba(240, 240, 240, 0.9);
}

/* 更改为 image 标签的样式 */
.back-icon-img {
    width: 70rpx; /* Adjusted size for better visual */
    height: 70rpx; /* Adjusted size for better visual */
    object-fit: contain; /* Ensure the image scales within the button without cropping */
    display: block; /* Remove extra space below inline elements */
}


.bottom{
	font-size: 15px;
	font-weight: 100;
	text-align: center;
	color: #7a776f;
    margin-top: 20rpx; /* Added margin for spacing */
}


    /* 顶部导航栏样式 */
    .top-nav {
        margin-top: calc(60rpx + env(safe-area-inset-top));
        margin-top: calc(60rpx + constant(safe-area-inset-top));
        margin-top: 20px;
        // display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 30rpx;
        // position: relative;
        z-index: 10;
    }

    .new-start-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #000000;
        text-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        letter-spacing: 4rpx;
    }

    .firework-btn {
        width: 80rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #f1eded, #ffffff);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
    }

    .firework-btn:active {
        transform: scale(0.9);
    }

    .firework-icon {
        font-size: 40rpx;
    }

    .top {
        text-align: center;
        font-size: 38rpx;
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 20rpx;
        padding: 30rpx 25rpx;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25rpx;
        backdrop-filter: blur(20rpx);
        border: none;
        box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        letter-spacing: 2rpx;
    }

    .top::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        animation: shimmer 3s infinite;
    }

    .top-title {
        display: block;
        font-size: 42rpx;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 8rpx;
        position: relative;
        z-index: 1;
    }

    .top-subtitle {
        display: block;
        font-size: 28rpx;
        font-weight: 400;
        color: #718096;
        position: relative;
        z-index: 1;
    }

    .banner-swiper {
        height: 420rpx;
        width: 92%;
        max-width: 700rpx;
        margin: 0 auto;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.12);
        border: 1rpx solid rgba(255, 255, 255, 0.3);
    }

    .swiper-item {
        height: 100%;
    }

    .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* 引言部分样式 */
    .intro-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40rpx 30rpx;
        min-height: 180rpx;
        background: transparent;
        margin-top: 20rpx;
        position: relative;
    }

    .intro-title {
        font-size: 52rpx;
        font-weight: 700;
        color: #4a5568;
        margin-bottom: 35rpx;
        letter-spacing: 4rpx;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .intro-content {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
    }

    .intro-text {
        font-size: 32rpx;
        color: #2d3748;
        white-space: pre-wrap;
        word-break: break-all;
        font-weight: 400;
        text-align: left;
        line-height: 1.8;
        position: relative;
        z-index: 1;
        letter-spacing: 1rpx;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        flex: 1;
        display: inline-block;
    }

    .typing-cursor {
        font-weight: bold;
        color: #667eea;
        margin-left: 5rpx;
        animation: blink 1s infinite step-end;
        font-size: 36rpx;
        line-height: 1.8;
        align-self: flex-start;
    }

    /* 动画定义 */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(30rpx); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 12rpx 30rpx rgba(102, 126, 234, 0.6);
        }
    }

    /* 烟花特效样式 */
    .firework-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .firework-container {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .firework-particle {
        position: absolute;
        border-radius: 50%;
        pointer-events: none;
    }

    .firework-text {
        position: relative;
        z-index: 10000;
        font-size: 48rpx;
        font-weight: bold;
        color: #fff;
        text-align: center;
        text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
        animation: textGlow 1s ease-in-out infinite alternate;
    }

    @keyframes fireworkExplode {
        0% {
            transform: scale(0) rotate(0deg);
            opacity: 1;
        }
        50% {
            transform: scale(1.5) rotate(180deg);
            opacity: 0.8;
        }
        100% {
            transform: scale(3) rotate(360deg);
            opacity: 0;
        }
    }

    @keyframes textGlow {
        0% {
            text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 20rpx rgba(255, 255, 255, 0.3);
        }
        100% {
            text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 40rpx rgba(255, 255, 255, 0.8);
        }
    }

    /* 统计信息弹窗样式 */
    .stats-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.6);
        z-index: 9998;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .stats-modal-content {
        background: #fff;
        border-radius: 20rpx;
        padding: 40rpx;
        width: 80%;
        max-width: 600rpx;
        box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        align-items: center;
        transform: scale(0.95);
        opacity: 0;
        animation: slideUp 0.3s forwards ease-out;
    }

    .modal-title {
        font-size: 40rpx;
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 30rpx;
        text-align: center;
    }

    .stats-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 15rpx;
        margin-bottom: 30rpx;
    }

    .stats-item {
        font-size: 30rpx;
        color: #4a5568;
        line-height: 1.5;
    }

    .close-modal-btn {
        background-color: #07c160;
        color: #fff;
        padding: 15rpx 40rpx;
        border-radius: 50rpx;
        font-size: 32rpx;
        font-weight: bold;
        margin-top: 20rpx;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.4);
    }

    .close-modal-btn:active {
        transform: scale(0.95);
        opacity: 0.8;
    }
</style>
