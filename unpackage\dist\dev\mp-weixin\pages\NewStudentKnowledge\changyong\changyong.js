"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "changyong",
  setup(__props) {
    const bannerList = common_vendor.ref([{
      src: "https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南"
    }]);
    common_vendor.onShareAppMessage(() => {
      return {
        title: "河南农业大学新生指南中心",
        // 分享卡的标题
        path: "/pages/index/index",
        // 分享后用户点击进入的页面路径，请确保路径正确
        imageUrl: bannerList.value[0].src
        // 分享卡上显示的图片，这里使用第一张轮播图
      };
    });
    const navigateToWebview = (externalUrl) => {
      const encodedUrl = encodeURIComponent(externalUrl);
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => navigateToWebview("https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE")),
        b: common_vendor.o(($event) => navigateToWebview("https://cwwx.henau.edu.cn/xysf/aAppPage/index.aspx?mac=70f02c7dd15ac82d13b3550bdf939810#/loginTemp/loginIng"))
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5c9efd14"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/changyong/changyong.js.map
