{"version": 3, "file": "index.js", "sources": ["pages/NewStudentKnowledge/navigation/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9uYXZpZ2F0aW9uL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 卡片列表区域，可垂直滚动 -->\n    <scroll-view class=\"card-list\" scroll-y=\"true\">\r\n\t\t\r\n\t\t\r\n\r\n\t\t\r\n\t\r\n\t\t\n      <!-- 卡片1：文化路校区 -->\n      <view class=\"card color-card-1\" @click=\"navigateTo('wenhua')\">\n        <!-- 背景图片 -->\n        <image class=\"card-image\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/68945f019f2b3_1754554113.png\" mode=\"aspectFill\"></image>\n        <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->\n        <view class=\"card-overlay\"></view>\n        <!-- 卡片内容区域 -->\n        <view class=\"card-content\">\n          <view class=\"card-text-container\">\n            <text class=\"card-title\">文化路校区</text>\n            <text class=\"card-subtitle\">历史底蕴，学术殿堂</text>\n          </view>\n          <!-- 箭头文本 -->\n          <text class=\"card-arrow-text\">></text>\n        </view>\n      </view>\n\n      <!-- 卡片2：龙子湖校区 -->\n      <view class=\"card color-card-2\" @click=\"navigateTo('longzihu')\">\n        <!-- 背景图片 -->\n        <image class=\"card-image\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/68945ea026d3f_1754554016.png\" mode=\"aspectFill\"></image>\n        <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->\n        <view class=\"card-overlay\"></view>\n        <!-- 卡片内容区域 -->\n        <view class=\"card-content\">\n          <view class=\"card-text-container\">\n            <text class=\"card-title\">龙子湖校区</text>\n            <text class=\"card-subtitle\">现代气息，活力新城</text>\n          </view>\n          <!-- 箭头文本 -->\n          <text class=\"card-arrow-text\">></text>\n        </view>\n      </view>\r\n\t  \r\n\t  \r\n\t  \r\n\t  <!-- 卡片3：许昌校区，纯灰色背景（无图片） -->\r\n\t  <view class=\"card grey-card\" @click=\"navigateTo('xuchang')\">\r\n\t  \t\t          <image class=\"card-image\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/68945d60691e0_1754553696.png\" mode=\"aspectFill\"></image>\r\n\t    <!-- 灰色卡片没有背景图片和覆盖层 -->\r\n\t    <view class=\"card-content\" @click=\"jumpxuchang()\">\r\n\t      <view class=\"card-text-container\">\r\n\t       <text class=\"card-title\">许昌校区</text>\r\n\t       <text class=\"card-subtitle\">星辰大海,等你征服</text>\r\n\t      </view>\r\n\t      <!-- 箭头文本 -->\r\n\t      <text class=\"card-arrow-text\">></text>\r\n\t    </view>\r\n\t  </view>\r\n\t  \r\n\t  \r\n\t  \r\n\t  \n\n\r\n\t  \r\n\t  \r\n\t  <!-- 卡片4：常用app -->\r\n\t  <view class=\"card color-card-2\" @click=\"navigateTo('changyong')\">\r\n\t    <!-- 背景图片 -->\r\n\t    <image class=\"card-image\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/689494fb37ec1_1754567931.png\" mode=\"aspectFill\"></image>\r\n\t    <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->\r\n\t    <view class=\"card-overlay\"></view>\r\n\t    <!-- 卡片内容区域 -->\r\n\t    <view class=\"card-content\">\r\n\t      <view class=\"card-text-container\">\r\n\t        <text class=\"card-title\">常用软件</text>\r\n\t        <text class=\"card-subtitle\">校园必备，尽在掌中</text>\r\n\t      </view>\r\n\t      <!-- 箭头文本 -->\r\n\t      <text class=\"card-arrow-text\">></text>\r\n\t    </view>\r\n\t  </view>\r\n\t  <view class=\"bottom\">\r\n\t  \t技术支持 河南农业大学IT工作室\r\n\t  </view>\n    </scroll-view>\n\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {};\n  },\n  methods: {\n    // 导航到指定校区的方法\n    navigateTo(campus) {\n      console.log(`导航到 ${campus} 校区`);\n      // 如果需要实际页面跳转，可以取消注释以下代码并配置对应的页面路径\n      uni.navigateTo({\n        url: `/pages/NewStudentKnowledge/${campus}/${campus}`\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh; /* 容器高度占满整个视口 */\n  background-color: #f7f7f7; /* 页面背景色 */\n}\n\n// 顶部状态栏占位，用于适配刘海屏等设备\n.header-status-bar {\n  height: var(--status-bar-height); /* uni-app 提供的状态栏高度变量 */\n  width: 100%;\n  background-color: #ffffff;\n}\n\n.header {\n  padding: 10px;\n  background-color: #ffffff;\n  border-bottom-left-radius: 20px;\n  border-bottom-right-radius: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* 底部阴影效果 */\n}\n.bottom{\n\tfont-size: 15px;\n\tfont-weight: 100;\n\ttext-align: center;\n\tcolor: #7a776f;\n}\n.header-title {\n  font-size: 28px;\n  font-weight: bold;\n  color: #333333;\n}\n\n.header-subtitle {\n  font-size: 14px;\n  color: #999999;\n}\n\n.card-list {\n  flex: 1; /* 占据剩余垂直空间 */\n  padding: 10px; /* 列表内边距，提供左右留白 */\n  overflow-y: auto; /* 允许垂直滚动 */\n  \n  // ===================== 卡片宽度控制区域 =====================\n  // 这里的 max-width 控制了卡片列表在宽屏设备上的最大宽度\n  // 调整这个值即可改变卡片的宽度，例如 350px\n  max-width: 350px;\n  margin: 0 auto; /* 左右自动外边距，实现居中 */\n  // ========================================================\n}\n\n.card {\n  position: relative; /* 相对定位，用于子元素的绝对定位 */\n  height: 200px;\n  margin-bottom: 5px; /* 卡片之间的间距 */\n  border-radius: 15px;\n  overflow: hidden; /* 隐藏超出圆角的部分 */\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 卡片阴影 */\n  display: flex; /* 使用 flex 布局 */\n  align-items: flex-end; /* 内容垂直对齐到底部 */\n  \n  transition: transform 0.2s ease-in-out; /* 点击时的动画效果 */\n  &:active {\n    transform: scale(0.98); /* 点击时轻微缩小 */\n  }\n}\n\n// 卡片背景图片样式\n.card-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1; /* 位于最底层 */\n  object-fit: cover; /* 保持图片比例并填充容器 */\n}\n\n// 图片上的半透明覆盖层，用于增强文字对比度\n.card-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent); /* 从下到上的黑色渐变 */\n  z-index: 2; /* 位于图片之上，内容之下 */\n}\n\n// 纯色卡片背景（当没有图片时使用）\n.color-card-1 {\r\n\tmargin-top: 5px;\n  background-color: #8c738e; /* 紫色系 */\n}\n\n.color-card-2 {\n  background-color: #4a90e2; /* 蓝色系 */\n}\n\n// 灰色卡片（许昌校区）\n.grey-card {\n  background-color: #e0e0e0;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05); /* 较浅的阴影 */\n}\n\n.card-content {\n  width: 100%;\n  padding: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  z-index: 3; /* 确保内容在最上层 */\n  color: #ffffff; /* 默认文字颜色为白色 */\n\n  // 灰色卡片中的文字颜色为深色\n  .grey-card & {\n    color: #333333;\n  }\n}\n\n.card-text-container {\r\n\t\n  display: flex;\n  flex-direction: column;\n}\n\n.card-title {\r\n\tcolor: white;\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.card-subtitle {\r\n\tcolor: white;\n  font-size: 14px;\n  margin-top: 5px;\n\n  // 灰色卡片中的副标题颜色\n  .grey-card & {\n  }\n}\n\n.card-arrow-text {\n  font-size: 24px;\n  font-weight: bold;\n  opacity: 0.6; /* 增加透明度，看起来更柔和 */\n}\n\n// 灰色卡片中的箭头文本颜色\n.grey-card .card-arrow-text {\n  color: #666666;\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/navigation/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4FA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;EACR;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,WAAW,QAAQ;AACjBA,gGAAY,OAAO,MAAM,KAAK;AAE9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,MAAM,IAAI,MAAM;AAAA,MACrD,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;ACzGA,GAAG,WAAW,eAAe;"}