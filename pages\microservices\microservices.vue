<template>
	<!-- <web-view src="https://microservices.henau.edu.cn/henauwfw/#/index"> -->
	<web-view src="https://microservice.leesong.top/henauwfw/#/index">
		<cover-view class="close-view" @click="closeView()">
			<cover-image class="close-icon" src="/static/icon/public/home.png"></cover-image>
		</cover-view>
	</web-view>
</template>

<script setup>
	import {
		onShareAppMessage,
		onShareTimeline,
		onHide
	} from '@dcloudio/uni-app'
	const closeView = () => {
		uni.reLaunch({
			url: '/pages/index/index'
		})
	}
	// 监听webview是否进后台，进后台则回到首页，清除页面栈
	// onHide(() => {
	// 	uni.reLaunch({
	// 		url: '/pages/index/index'
	// 	})
	// })
	onShareAppMessage((options) => {
		// console.log(options.webViewUrl)
		// 获取的是h5的真实地址
		// uni.showModal({
		// 	title: '有确认取消的弹窗',
		// 	content: options.webViewUrl,
		// 	success: function(res) {
		// 		if (res.confirm) {
		// 			console.log('点击了确认')
		// 		} else {
		// 			console.log('点击了取消')
		// 		}
		// 	}
		// })
		// 获取 webViewUrl
		const webViewUrl = options.webViewUrl;
		// 构建携带参数的路径
		const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;
		return {
			path: sharePath,
		}
	})
	onShareTimeline(() => {})
</script>

<style>
	.close-view {
		background-color: #616161;
		border-radius: 50%;
		position: fixed;
		z-index: 99999;
		bottom: 19vh;
		right: 30px;
		visibility: visible !important;
		padding: 5px;
	}

	.close-icon {
		width: 30px;
		height: 30px;
	}
</style>