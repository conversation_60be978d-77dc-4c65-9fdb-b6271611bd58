"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "web-view",
  setup(__props) {
    const url = common_vendor.ref("");
    const loading = common_vendor.ref(true);
    common_vendor.onLoad((options) => {
      if (options.url) {
        url.value = decodeURIComponent(options.url);
        common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/web-view/web-view.vue:24", "加载外部链接:", url.value);
      } else {
        common_vendor.index.__f__("error", "at pages/NewStudentKnowledge/web-view/web-view.vue:27", "未接收到有效的URL参数");
        common_vendor.index.showToast({
          title: "链接无效",
          icon: "none"
        });
        loading.value = false;
      }
    });
    const onWebviewLoad = () => {
      common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/web-view/web-view.vue:38", "Webview加载成功");
      loading.value = false;
    };
    const onWebviewError = (e) => {
      common_vendor.index.__f__("error", "at pages/NewStudentKnowledge/web-view/web-view.vue:44", "Webview加载失败:", e.detail);
      common_vendor.index.showToast({
        title: "链接加载失败，请检查网络或稍后重试",
        icon: "none",
        duration: 3e3
      });
      loading.value = false;
    };
    return (_ctx, _cache) => {
      return {
        a: url.value,
        b: common_vendor.o(onWebviewLoad),
        c: common_vendor.o(onWebviewError)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-022df0fa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/web-view/web-view.js.map
