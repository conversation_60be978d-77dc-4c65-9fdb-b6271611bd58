/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
  padding: 15rpx;
  padding-top: calc(15rpx + env(safe-area-inset-top));
  padding-top: calc(15rpx + constant(safe-area-inset-top));
  padding-bottom: calc(15rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(15rpx + constant(safe-area-inset-bottom));
  box-sizing: border-box;
  position: relative;
}

/* 返回按钮样式 */
.back-button {
  position: absolute;
  top: calc(55rpx + env(safe-area-inset-top));
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  /* Ensure image doesn't overflow rounded button */
}
.back-button:active {
  transform: scale(0.9);
  background: rgba(240, 240, 240, 0.9);
}

/* 更改为 image 标签的样式 */
.back-icon-img {
  width: 70rpx;
  /* Adjusted size for better visual */
  height: 70rpx;
  /* Adjusted size for better visual */
  object-fit: contain;
  /* Ensure the image scales within the button without cropping */
  display: block;
  /* Remove extra space below inline elements */
}
.bottom {
  font-size: 15px;
  font-weight: 100;
  text-align: center;
  color: #7a776f;
  margin-top: 20rpx;
  /* Added margin for spacing */
}

/* 顶部导航栏样式 */
.top-nav {
  margin-top: calc(60rpx + env(safe-area-inset-top));
  margin-top: calc(60rpx + constant(safe-area-inset-top));
  margin-top: 20px;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  z-index: 10;
}
.new-start-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  text-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  letter-spacing: 4rpx;
}
.firework-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #f1eded, #ffffff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}
.firework-btn:active {
  transform: scale(0.9);
}
.firework-icon {
  font-size: 40rpx;
}
.top {
  text-align: center;
  font-size: 38rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 20rpx;
  padding: 30rpx 25rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: none;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  letter-spacing: 2rpx;
}
.top::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 3s infinite;
}
.top-title {
  display: block;
  font-size: 42rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}
.top-subtitle {
  display: block;
  font-size: 28rpx;
  font-weight: 400;
  color: #718096;
  position: relative;
  z-index: 1;
}
.banner-swiper {
  height: 420rpx;
  width: 92%;
  max-width: 700rpx;
  margin: 0 auto;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.swiper-item {
  height: 100%;
}
.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 引言部分样式 */
.intro-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  min-height: 180rpx;
  background: transparent;
  margin-top: 20rpx;
  position: relative;
}
.intro-title {
  font-size: 52rpx;
  font-weight: 700;
  color: #4a5568;
  margin-bottom: 35rpx;
  letter-spacing: 4rpx;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}
.intro-content {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}
.intro-text {
  font-size: 32rpx;
  color: #2d3748;
  white-space: pre-wrap;
  word-break: break-all;
  font-weight: 400;
  text-align: left;
  line-height: 1.8;
  position: relative;
  z-index: 1;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  flex: 1;
  display: inline-block;
}
.typing-cursor {
  font-weight: bold;
  color: #667eea;
  margin-left: 5rpx;
  animation: blink 1s infinite step-end;
  font-size: 36rpx;
  line-height: 1.8;
  align-self: flex-start;
}

/* 动画定义 */
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp {
from {
    transform: translateY(30rpx);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes pulse {
0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
}
50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 30rpx rgba(102, 126, 234, 0.6);
}
}
/* 烟花特效样式 */
.firework-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}
.firework-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.firework-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}
.firework-text {
  position: relative;
  z-index: 10000;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
  animation: textGlow 1s ease-in-out infinite alternate;
}
@keyframes fireworkExplode {
0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
}
50% {
    transform: scale(1.5) rotate(180deg);
    opacity: 0.8;
}
100% {
    transform: scale(3) rotate(360deg);
    opacity: 0;
}
}
@keyframes textGlow {
0% {
    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 20rpx rgba(255, 255, 255, 0.3);
}
100% {
    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 40rpx rgba(255, 255, 255, 0.8);
}
}
/* 统计信息弹窗样式 */
.stats-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}
.stats-modal-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 80%;
  max-width: 600rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.95);
  opacity: 0;
  animation: slideUp 0.3s forwards ease-out;
}
.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 30rpx;
  text-align: center;
}
.stats-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-bottom: 30rpx;
}
.stats-item {
  font-size: 30rpx;
  color: #4a5568;
  line-height: 1.5;
}
.close-modal-btn {
  background-color: #07c160;
  color: #fff;
  padding: 15rpx 40rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.4);
}
.close-modal-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}