{"version": 3, "file": "ac.js", "sources": ["pages/ac/ac.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWMvYWMudnVl"], "sourcesContent": ["<template>\r\n\t<web-view :src=\"webviewUrl\">\r\n\t\t<cover-view class=\"close-view\" @click=\"closeView()\">\r\n\t\t\t<cover-image class=\"close-icon\" src=\"/static/icon/public/home.png\"></cover-image>\r\n\t\t</cover-view>\r\n\t</web-view>\r\n</template>\r\n\r\n<script setup>\r\n\timport {\r\n\t\tref,\r\n\t\tonMounted,\r\n\t\twatch,\r\n\t\tonUnmounted\r\n\t} from \"vue\";\r\n\timport {\r\n\t\tgetCurrentInstance\r\n\t} from \"vue\";\r\n\timport {\r\n\t\tonHide,\r\n\t\tonUnload,\r\n\t\tonBackPress,\r\n\t\tonShareAppMessage,\r\n\t\tonShareTimeline,\r\n\t} from \"@dcloudio/uni-app\";\r\n\r\n\t// 获取当前实例，用于获取页面传递过来的参数\r\n\tconst {\r\n\t\tproxy\r\n\t} = getCurrentInstance();\r\n\r\n\t// 用于存储传递过来的webview链接，初始化为空字符串\r\n\tconst webviewUrl = ref(\"\");\r\n\r\n\t// 在组件挂载后获取传递过来的参数（假设是从上个页面通过路由传参的方式传递过来的）\r\n\tonMounted(() => {\r\n\t\tconst pages = getCurrentPages();\r\n\t\tconst currentPage = pages[pages.length - 1];\r\n\t\tconst options = currentPage.options;\r\n\t\tif (options.webviewUrl) {\r\n\t\t\twebviewUrl.value = decodeURIComponent(options.webviewUrl);\r\n\t\t}\r\n\t});\r\n\t// 针对电子通行证场景，退出该页面后自动返回小程序首页\r\n\tonHide(() => {\r\n\t\tuni.reLaunch({\r\n\t\t\turl: '/pages/index/index'\r\n\t\t});\r\n\t});\r\n\tonUnload(() => {});\r\n\tonBackPress((options) => {});\r\n\tconst closeView = () => {\r\n\t\tuni.reLaunch({\r\n\t\t\turl: \"/pages/index/index\",\r\n\t\t});\r\n\t};\r\n\tonShareAppMessage((options) => {\r\n\t\t// 获取 webViewUrl\r\n\t\tconst webViewUrl = options.webViewUrl;\r\n\t\t// 构建携带参数的路径\r\n\t\tconst sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(\r\n\t\twebViewUrl\r\n\t)}`;\r\n\t\treturn {\r\n\t\t\tpath: sharePath,\r\n\t\t};\r\n\t});\r\n\tonShareTimeline(() => {});\r\n</script>\r\n\r\n<style>\r\n\t.close-view {\r\n\t\tbackground-color: #616161;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: fixed;\r\n\t\tz-index: 99999;\r\n\t\tbottom: 19vh;\r\n\t\tright: 30px;\r\n\t\tvisibility: visible !important;\r\n\t\tpadding: 5px;\r\n\t}\r\n\r\n\t.close-icon {\r\n\t\twidth: 30px;\r\n\t\theight: 30px;\r\n\t}\r\n</style>", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/ac/ac.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "onHide", "uni", "onUnload", "onBackPress", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": ";;;;;;AAgCA,UAAA,aAAAA,kBAAA,EAAA;AAGAC,kBAAAA,UAAA,MAAA;AACA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA;AACA,UAAA,QAAA,YAAA;AACA,mBAAA,QAAA,mBAAA,QAAA,UAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAAA,OAAA,MAAA;AACAC,oBAAAA,MAAA,SAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA,CAAA;AACAC,kBAAA,SAAA,MAAA;AAAA,IAAA,CAAA;AACAC,8BAAA,CAAA,YAAA;AAAA,IAAA,CAAA;AACA,UAAA,YAAA,MAAA;AACAF,oBAAAA,MAAA,SAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AACAG,kBAAA,kBAAA,CAAA,YAAA;AAEA,YAAA,aAAA,QAAA;AAEA,YAAA,YAAA,iCAAA;AAAA,QACA;AAAA,MACA,CAAA;AACA,aAAA;AAAA,QACA,MAAA;AAAA,MACA;AAAA,IACA,CAAA;AACAC,kBAAA,gBAAA,MAAA;AAAA,IAAA,CAAA;;;;;;;;;;;AClEA,GAAG,WAAWC,SAAe;"}