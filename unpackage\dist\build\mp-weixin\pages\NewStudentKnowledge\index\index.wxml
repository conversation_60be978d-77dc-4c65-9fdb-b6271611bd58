<view class="page-container" bindtap="{{w}}"><view class="top"><text class="top-title">河南农业大学</text><text class="top-subtitle">新生指南中心</text></view><view class="back-button" catchtap="{{a}}"><image class="back-icon-img" src="https://itstudio.henau.edu.cn/image_hosting/uploads/68960239b210b_1754661433.png" alt="返回"></image></view><swiper indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{1000}}" circular="{{true}}" indicator-active-color="#07c160" class="banner-swiper" bindchange="{{c}}"><swiper-item wx:for="{{b}}" wx:for-item="item" wx:key="c" class="swiper-item"><image class="banner-image" src="{{item.a}}" mode="aspectFill" binderror="{{item.b}}"></image></swiper-item></swiper><view class="intro-box" bindtap="{{f}}"><text class="intro-title">引言</text><view class="intro-content"><text class="intro-text">{{d}}</text><text wx:if="{{e}}" class="typing-cursor">|</text></view></view><view wx:if="{{g}}" class="firework-overlay" bindtap="{{i}}"><view class="firework-container"><view wx:for="{{h}}" wx:for-item="firework" wx:key="a" class="firework-particle" style="{{firework.b}}"></view></view><text class="firework-text">🎉 欢迎开启新的人生篇章！🎉</text></view><view wx:if="{{j}}" class="stats-modal-overlay" bindtap="{{r}}"><view class="stats-modal-content" catchtap="{{q}}"><text class="modal-title">用户访问统计</text><view wx:if="{{k}}" class="stats-section"><text class="stats-item">总用户数: {{l}}</text><text class="stats-item">活跃用户: {{m}}</text><text class="stats-item">新用户: {{n}}</text><text class="stats-item">总访问量: {{o}}</text></view><text wx:else class="stats-item">加载统计数据中...</text><text class="close-modal-btn" bindtap="{{p}}">关闭</text></view></view><view wx:if="{{s}}" class="bottom"><text>技术支持 河南农业大学IT工作室</text><text wx:if="{{t}}"> | 访问量: {{v}}</text></view></view>