"use strict";
const common_vendor = require("../../../common/vendor.js");
const qa = [
  {
    question: "Q1：学校今年的校历和作息时间是什么样的？",
    answer: "",
    image_url: "https://itstudio.henau.edu.cn/image_hosting/uploads/68942481b8bf0_1754539137.jpg",
    URl: ""
  },
  {
    question: "Q2：入学报到需要准备哪些物品？",
    answer: "A2：报到当天，你一定要带好录取通知书、高考准考证份证及其复印件、学籍档案，以及近期拍摄的一寸和二寸照片（红、蓝、白底都准备一些）。这些都是办理入学手续必需的证件。生活用品可以到校后再购买，学校周围商店都能买到。"
  },
  {
    question: "Q3：学费和生活费怎么缴纳？",
    answer: "A3：学费通常需要在开学前缴纳，最晚可以在年前完成。如果办理了助学贷款，就不需要担心这个问题。缴费方式以微信或银行卡支付为主。"
  },
  {
    question: "Q4：入学考试是什么？",
    answer: "A4：开学第一周会组织一次入学考试，但这仅用于核验笔迹，不出成绩，后续不会有不良影响。"
  },
  {
    question: "Q5：助学金、奖学金怎么申请？",
    answer: "A5：奖学金（如国家奖学金、校级奖学金）主要看你的绩点和综合表现。助学金是针对家庭经济困难学生的，申请过程并不复杂。"
  },
  {
    question: "Q6：证件照需要准备多少？",
    answer: "A6：建议准备蓝、白底的近期免冠正面半身一寸照至少12张，两寸照2-4张。办理学生证、团关系转接、学籍档案、社团会员证等都需要用到。"
  },
  {
    question: "Q7：新生开学要带现金吗？",
    answer: "A7：最好不要带太多现金。建议将学费和生活费存入银行卡，随身只携带少量现金即可。"
  },
  {
    question: "Q8：录取通知书开学需要上交吗？",
    answer: "A8：报到当天需要上交录取通知书。"
  },
  {
    question: "Q9：高考档案袋可以打开吗？",
    answer: "A9：高考考生档案袋密封后千万不要打开。"
  },
  {
    question: "Q10：准考证丢了怎么办？",
    answer: "A10：你可以登录考试院官网自行打印。"
  },
  {
    question: "Q11：平时外出需要请假吗？",
    answer: "A11：如果是在非上课时间外出，通常不用请假。但夜不归宿或其他特殊情况需要向辅导员或任课老师请假。"
  },
  {
    question: "Q12：上课需要交手机吗？",
    answer: "A12：这要看具体老师的要求。有的老师会让学生上课时交手机，所以可以带一个备用机到学校，方便有时候让室友帮忙签到。"
  },
  {
    question: "Q13：宿舍里可以使用吹风机、卷发棒、冰箱、锅吗？",
    answer: "A13：不可以。学校为了安全会限制大功率电器，否则会跳闸。学校会不定期检查，违规会被通报，影响之后保研。"
  },
  {
    question: "Q14：有没有早操和晚自习？",
    answer: "A14：没有早操。早晚自习要看学院安排，不同学院要求不同。许昌校区的软件学院有早晚自习。"
  },
  {
    question: "Q15：宿舍是几人间？有没有独立卫浴？",
    answer: "A15：宿舍通常是四人间或六人间，桃李园学生公寓都有独卫，文化路校区宿舍部分有独卫。新生主要在文化路校区和桃李园学生公寓。许昌校区软件学院的新楼有独立卫浴，老楼则没有。"
  },
  {
    question: "Q16：宿舍床位怎么分？",
    answer: "A16：宿舍是学校分好的，自己选床位，先到先得。信管学院是自助选宿舍（软件学院除外，学校会分好宿舍）。"
  },
  {
    question: "Q17：宿管查寝规律吗？",
    answer: "A17：宿管通常不查寝，但有的学院学生会会查寝，要求不同。许昌校区会随机检查晚归情况。"
  },
  {
    question: "Q18：学校周围有没有小吃街或好玩的地方？",
    answer: "A18：文化路校区和桃李园校区在市中心，周围有很多小吃。许昌校区附近有北海胖东来，也可以去市中心玩，晚上校门口有小吃摊。"
  },
  {
    question: "Q19：需要办理水卡或饭卡吗？",
    answer: "A19：不需要办水卡，每层都有饮水机，扫码即可付款。饭卡就是校园餐卡，开学当天会统一发放，也有电子版。"
  },
  {
    question: "Q20：宿舍床的规格多大？能挂床帘吗？",
    answer: "A20：床的规格是0.95米 x 1.95米。可以挂床帘，每个宿舍都配有空调。军训期间涉及整理内务，建议军训后再挂。"
  },
  {
    question: "Q21：宿舍每天会断电熄灯吗？",
    answer: "A21：宿舍不会断电，可以全天自由使用。"
  },
  {
    question: "Q22：洗澡方便吗？",
    answer: "A22：文化路校区的宿舍需要去澡堂洗，没有隔间。桃李园的学生可以在宿舍楼一楼的澡堂洗，有隔间。许昌校区的大澡堂都是单间。"
  },
  {
    question: "Q23：校园网怎么样？",
    answer: "A23：学校有一个免费的校园网，但网速可能比较慢。郑州校区可办理联通或移动的校园电话卡，网速会快一些。许昌校区只有教学楼有免费的校园网，宿舍使用校园网需办理校园卡，一张卡最多连接两个设备。"
  },
  {
    question: "Q24：校园里吃饭、购物、取快递方便吗？",
    answer: "A24：各校区都有多个食堂和超市。快递点集中在生活区。校园卡（一卡通）是“万能卡”，可用于吃饭、洗澡、打水、借书等，支持线上充值。（此校园卡为学生卡，注意和移动联通的校园电话卡区分，不是一个东西）"
  },
  {
    question: "Q25：电动车充电要买电卡吗？",
    answer: "A25：不需要，扫码支付即可。学校有充电桩。"
  },
  {
    question: "Q26：洗澡用水、喝水需要花钱吗？",
    answer: "A26：需要扫码支付。不过学校也有免费的开水房。"
  },
  {
    question: "Q27：宿舍空调问题？",
    answer: "A27：宿舍为四人间或六人间，有上床下桌或上下铺。所有宿舍都配有空调（租赁），电费自付，可以通过“河南农业大学信息化办公室”微信公众号充值。"
  },
  {
    question: "Q28：学校供水供电、校园跑、志愿时长情况？",
    answer: "A28：学校24小时不断水断电，暂无强制的校园跑，也不需要志愿时长。宿舍空调可以全天自由开关。"
  },
  {
    question: "Q29：桃李园上课问题？",
    answer: "A29：桃李园校区没有操场和图书馆，但有教室。桃李园的学生大部分课程都在本校区上，少部分课程需要到文化路校区。"
  },
  {
    question: "Q30：可以提前报到和进宿舍吗？",
    answer: "A30：为了大家的安全，学校不允许提前进宿舍。报到当天最早早上六点多就可以办理入学手续了。如果你是外省同学，可以提前在学校附近预订酒店。"
  },
  {
    question: "Q31：军训裤子松不松？",
    answer: "A31：主要看尺寸，也会发腰带。"
  },
  {
    question: "Q32：军训服需要买学校的吗？",
    answer: "A32：建议统一购买学校的军训服，迎新系统开放后就可以购买了。"
  },
  {
    question: "Q33：周末还军训吗？会放假吗？",
    answer: "A33：军训期间不放假，会一直持续到军训结束。"
  },
  {
    question: "Q34：床上用品需要统一购买吗？",
    answer: "A34：不需要统一购买，是自愿的，可以自己准备。"
  },
  {
    question: "Q35：专业课难不难？容不容易挂科？",
    answer: "A35：只要你上课认真听讲，课后稍加练习，通常都不会挂科。老师批改试卷不会特别宽松，但平时分也会起到一定的帮助作用。专业课还是有一定难度的，需要认真学习。"
  },
  {
    question: "Q36：可以转专业吗？",
    answer: "A36：可以。大一下学期六月之后会发布转专业通知，可以申请。但转专业有难度。最有效的办法是提高大一上学期的成绩。参军入伍可以随意转专业。\n注意事项：有且仅有大一可以转专业，非同一批次间不能互相转，高收费不能转普通专业，许昌校区不能转郑州校区。（退伍后忽略此注意事项）"
  },
  {
    question: "Q37：转到热门专业容易吗？",
    answer: "A37：很难，因为竞争压力很大。（如法学、烟草、计算机等）"
  },
  {
    question: "Q38：体育课都能抢到什么？",
    answer: "A38：有很多选择，比如篮球、羽毛球、乒乓球、足球等。学校会提前通知选课时间。"
  },
  {
    question: "Q39：大一可以考四六级吗？",
    answer: "A39：原则上大一不允许考四六级，神农班待定。"
  },
  {
    question: "Q40：常用APP有哪些？",
    answer: "A40：中国大学MOOC、学习通、喜鹊儿、WE Learn、大白U帮。"
  },
  {
    question: "Q41：怎么选课？",
    answer: "A41：学校会在军训期间安排，并发布通知，大家不用担心。每年体育课的选择很多，届时需要拼网速抢课。"
  },
  {
    question: "Q42：学校快递点地址？",
    answer: "A42：\n文化路校区：河南省郑州市金水区农业路63号河南农业大学文化路校区。\n桃李园地址：郑州市金水区信息学院路8号桃李园学生公寓。\n第三生活区：郑州市金水区信息学院路7号附近。\n龙子湖校区：河南省郑州市金水区龙子湖街道河南农业大学龙子湖校区菜鸟驿站。\n许昌校区：河南省许昌市建安区河南农业大学。\n（不建议开学直接寄到学校，因为快递会堆积成山，可以寄到学校附近的快递点。）"
  },
  {
    question: "Q43：有哪些注意事项？",
    answer: "A43：不要点击陌生链接，特别是QQ邮箱链接。学校不会通过QQ邮箱发信息。不要泄露身份证号、学生证、饭卡、银行卡号等信息。"
  },
  {
    question: "Q44：军训时有哪些注意事项？",
    answer: "A44：注意防晒，根据自身身体状况调节强度。如果身体感到不舒服，要及时和教官或助班说，会安排休息。"
  },
  {
    question: "Q45：勤工助学求助？",
    answer: "A45：学校附近有很多兼职机会，比如家教、饭店服务员等。学校食堂也有勤工助学岗位，学校部分行政部门也会招学生助理，可以自行了解或找辅导员咨询。"
  },
  {
    question: "Q46：学校旁边有驾校吗？",
    answer: "A46：有，学校附近的驾校都有优惠政策。"
  },
  {
    question: "Q47：入学会有指引吗？会不会迷路？",
    answer: "A47：入学时会有很多志愿者引导，不用担心迷路。"
  },
  {
    question: "Q48：校医院能看病吗？报销方便吗？",
    answer: "A48：校医院可以看常见病。在校医院首诊后转诊校外，符合条件可以按比例报销，具体流程关注校医院通知。"
  },
  {
    question: "Q49：关于校园电话卡需不需要办？",
    answer: "A49：校园卡主要用于连接校园网。如果宿舍手机信号差、流量不够用或者爱玩游戏，可以办理，也可以自行选择办理校外流量卡。校园卡是自愿购买的，不会和选课挂钩。"
  },
  {
    question: "Q50：军训免训问题？",
    answer: "A50：需要医院证明才能申请免训。高度近视也需要参加军训。如果身体不适，可以直接和教官说，会安排休息。"
  },
  {
    question: "Q51：关于烟草各种问题解答？",
    answer: "A51：\n烟草就业：如卷烟厂等。就业面较窄，就业前景可以自行查询。\n上课抽烟：烟草专业上课不能抽烟，这和是否是烟草专业无关，（品烟课除外）\n宿舍问题：烟草专业大一通常在老校区（可能在桃李园），可能是四人寝，可能有独卫。\n进烟草行业：主要靠个人实力，没有实力很难进入。"
  },
  {
    question: "Q52：迎新系统什么时候开？",
    answer: "A52：迎新系统通常在每年8月底开放。"
  },
  {
    question: "Q53：河南农业大学招生办电话？",
    answer: "A53：全国招生咨询热线：0371-56990360 / 56990366 / 56990368 / 56990389。"
  },
  {
    question: "Q54：迎新系统是干嘛的？",
    answer: "A54：迎新系统用于补充新生信息、完成入学答题和购买军训服。"
  },
  {
    question: "Q55：助班什么时候出来？",
    answer: "A55：去年是在8月24日左右。分班后，助班会联系你并拉你进班级群。"
  },
  {
    question: "Q56：什么时候分班？",
    answer: "A56：学校会提前分好班，去年大约是在8月18日左右。"
  },
  {
    question: "Q57：学号有什么用？",
    answer: "A57：学号是学生的个人ID，每个人都有一个对应的独一无二的学号。"
  },
  {
    question: "Q58：报到的地方有停车场吗？",
    answer: "A58：报到当天人会非常多，可能会堵车。其中桃李园必堵。"
  },
  {
    question: "Q59：团员档案转到哪里？",
    answer: "A59：分班后，助班会通知并安排转到班级团支部。"
  },
  {
    question: "Q60：助班什么时候建班级群？",
    answer: "A60：通常在8月中旬至8月底，不同学院进度不同。"
  },
  {
    question: "Q61：自己办的银行卡和学校统一办的一样怎么办？",
    answer: "A61：如果你已经办了和学校统一办的一样的农行银行卡，开学时学校就不会再为你办理了。"
  },
  {
    question: "Q62：军训严不严？",
    answer: "A62：强度适中，很人性化。"
  },
  {
    question: "Q63:医疗保险如何办理",
    answer: "A63：\n参保建议：建议在家庭所在地或学校中选择待遇更优的一处参保，切勿重复参保。\n参保时间：助班或者负责此部分的班干部会及时传达参保事项及流程。\n学校医保：目前根据政策，费用约为 390元/年 （比普通居民医保400元便宜10元），属于城乡居民医疗保险。\n异地就医：参保地为郑州或许昌。异地根据当地政策。"
  },
  {
    question: "Q64：为什么通知书这么简陋？",
    answer: "A64：学校提倡“优足常乐”，前几届的通知书都只是一张纸。"
  },
  {
    question: "Q65：助学贷款银行卡问题？",
    answer: "A65：可以咨询当地助学贷款部门，或者等分班后询问助理班主任或辅导员。"
  },
  {
    question: "Q66：团关系怎么转？",
    answer: "A66：去年团关系是在8月16日左右开始办理。学校公众号会发通知，不用着急。"
  },
  {
    question: "Q67：学费什么时候交，怎么交？",
    answer: "A67：学费一般在开学前，最晚年前缴费即可。办理助学贷款的同学不需要管，学校会直接从助学贷款中扣除学费住宿费部分，如有结余的同学可自行分配剩余资产。可在“河南农业大学财务处”微信服务号——学生缴费处进行缴费。"
  }
];
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const showContributorModal = common_vendor.ref(false);
    const contributors = common_vendor.ref([
      { category: "河南农业大学IT工作室成员", names: ["24级CLOWN", "24级千木", "24级非酋sama", "24级XX", "24级斯特", "24级阿布学长", "24级槿熙", "24级昔文", "24级Pinging"] },
      { category: "其他贡献者", names: ["22级卢荟胶", "23级鲤鱼"] }
    ]);
    const showContributors = () => {
      showContributorModal.value = true;
    };
    const closeContributorsModal = () => {
      showContributorModal.value = false;
    };
    const readingProgress = common_vendor.ref(0);
    let totalScrollHeight = 0;
    common_vendor.onReady(() => {
      common_vendor.index.createSelectorQuery().select(".page-container").boundingClientRect((pageRect) => {
        if (pageRect) {
          totalScrollHeight = pageRect.height - common_vendor.index.getSystemInfoSync().windowHeight;
          if (totalScrollHeight < 0)
            totalScrollHeight = 0;
        }
      }).exec();
    });
    common_vendor.onPageScroll((e) => {
      if (totalScrollHeight > 0) {
        readingProgress.value = e.scrollTop / totalScrollHeight * 100;
        if (readingProgress.value > 100)
          readingProgress.value = 100;
      } else {
        readingProgress.value = 0;
      }
    });
    const scrollToTop = () => {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    };
    const parseAnswer = (answerString) => {
      const parts = [];
      const regex = /<text class="highlight">(.*?)<\/text>/g;
      let lastIndex = 0;
      let match;
      while ((match = regex.exec(answerString)) !== null) {
        if (match.index > lastIndex) {
          parts.push({ type: "text", content: answerString.substring(lastIndex, match.index) });
        }
        parts.push({ type: "highlight", content: match[1] });
        lastIndex = regex.lastIndex;
      }
      if (lastIndex < answerString.length) {
        parts.push({ type: "text", content: answerString.substring(lastIndex) });
      }
      return parts;
    };
    const registrationQa = common_vendor.ref(qa);
    const previewImage = (imageUrl) => {
      common_vendor.index.navigateTo({
        url: `/pages/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(showContributors),
        b: common_vendor.f(registrationQa.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.question),
            b: common_vendor.f(parseAnswer(item.answer), (part, pIndex, i1) => {
              return {
                a: common_vendor.t(part.content),
                b: pIndex,
                c: part.type === "highlight" ? 1 : ""
              };
            }),
            c: item.image_url != null
          }, item.image_url != null ? {
            d: item.image_url,
            e: common_vendor.o(($event) => previewImage(item.image_url), "reg-" + index)
          } : {}, {
            f: "reg-" + index
          });
        }),
        c: showContributorModal.value
      }, showContributorModal.value ? {
        d: common_vendor.f(contributors.value, (category, catIndex, i0) => {
          return {
            a: common_vendor.t(category.category),
            b: common_vendor.f(category.names, (name, nameIndex, i1) => {
              return {
                a: common_vendor.t(name),
                b: "name-" + nameIndex
              };
            }),
            c: "cat-" + catIndex
          };
        }),
        e: common_vendor.o(closeContributorsModal),
        f: common_vendor.o(() => {
        }),
        g: common_vendor.o(closeContributorsModal)
      } : {}, {
        h: common_vendor.t(Math.round(readingProgress.value)),
        i: common_vendor.o(scrollToTop)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cc3a1b72"]]);
_sfc_main.__runtimeHooks = 1;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/comment/index.js.map
