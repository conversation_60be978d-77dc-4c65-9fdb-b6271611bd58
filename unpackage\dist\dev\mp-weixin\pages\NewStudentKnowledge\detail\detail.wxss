
.detail-page.data-v-466d9e6e {
	display: flex;
	flex-direction: column; /* 调整为列布局，以便提示文字在底部 */
	justify-content: center;
	align-items: center;
	width: 100vw;
	height: 100vh;
	background-color: #000; /* 黑色背景，更好地展示图片 */
	position: fixed; /* 固定定位，确保覆盖整个屏幕 */
	top: 0;
	left: 0;
	z-index: 9999; /* 确保在最上层 */
	overflow: hidden; /* 防止页面滚动 */
}
.movable-area.data-v-466d9e6e {
	width: 100%;
	height: 100%;
	/* 确保 movable-area 能够覆盖整个可见区域，以便拖动和缩放 */
}
.movable-view.data-v-466d9e6e {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	/* movable-view 的大小通常设置为 100% 100% */
}
.full-image.data-v-466d9e6e {
	width: 100%; /* 图片宽度填充 movable-view */
	height: auto; /* 高度自适应，保持图片比例 */
	/* mode="widthFix" 确保图片宽度固定，高度自适应 */
}
.close-tip.data-v-466d9e6e {
	position: absolute;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	padding: 10rpx 30rpx;
	border-radius: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	white-space: nowrap;
	z-index: 10000; /* 确保提示文字在最上层 */
}
