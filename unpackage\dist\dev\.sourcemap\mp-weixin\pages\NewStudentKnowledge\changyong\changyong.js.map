{"version": 3, "file": "changyong.js", "sources": ["pages/NewStudentKnowledge/changyong/changyong.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9jaGFuZ3lvbmcvY2hhbmd5b25nLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <view class=\"header\">\n      <text class=\"header-title\">常用软件与平台</text>\n    </view>\n\n    <view class=\"card-section\">\n      <view class=\"card\">\n        <view class=\"card-title-container\">\n          <text class=\"card-title\">常用平台</text>\n        </view>\n\n        <view class=\"platform-item\">\n          <text class=\"item-title\">河南农业大学专属学习交流平台</text>\n          <view class=\"link-group\">\n            <!-- 农宝圈链接，通过navigateToWebview方法跳转到web-view页面 -->\n            <view class=\"link-button\" @click=\"navigateToWebview('https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE')\">\n              <text>农宝圈</text>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"platform-item\">\n          <text class=\"item-title\">学生卡充值、电费充值</text>\n          <!-- 学生卡充值链接，通过navigateToWebview方法跳转到web-view页面 -->\n          <view>\n            <text class=\"text\">请搜索\"河南农业大学信息化办公室\"公众号点击\"校园卡\"选项</text>\n          </view>\n        </view>\n\n        <view class=\"platform-item\">\n          <text class=\"item-title\">学费缴纳</text>\n          <!-- 学费缴纳链接，通过navigateToWebview方法跳转到web-view页面 -->\n          <view class=\"link-button\" @click=\"navigateToWebview('https://cwwx.henau.edu.cn/xysf/aAppPage/index.aspx?mac=70f02c7dd15ac82d13b3550bdf939810#/loginTemp/loginIng')\">\n            <text>河南农业大学财务处</text>\n          </view>\n          <text class=\"note-text\">（用户名为学号，密码默认为身份证后六位）</text>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"card-section\">\n      <view class=\"card\">\n        <view class=\"card-title-container\">\n          <text class=\"card-title\">常用软件</text>\n        </view>\n        <!-- 喜鹊儿图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbcfaa122_1754577871.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">喜鹊儿</text>\n          </view>\n          <text class=\"item-desc\">查课表、查成绩、选课、申请调课等</text>\n        </view>\n        <!-- 学习通图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc14ee537_1754577940.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">学习通</text>\n          </view>\n          <text class=\"item-desc\">刷网课、提交作业</text>\n        </view>\n        <!-- 大学生MOOC图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd621ad7_1754577878.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">大学生MOOC</text>\n          </view>\n          <text class=\"item-desc\">刷网课、提交作业</text>\n        </view>\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n    \r\n\t\t\t   <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc7a903c2_1754578042.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">WE Learn ,词达人</text>\n          </view>\n          <text class=\"item-desc\">大英刷课用</text>\n        </view>\n        <!-- 胖乖生活图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc51c2a4a_1754578001.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">胖乖生活(许昌，龙子湖使用)</text>\n          </view>\n          <text class=\"item-desc\">学校澡堂洗澡要用</text>\n        </view>\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n           \t\t\t   <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd142a34_1754577873.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">大白U帮(桃李园用)</text>\n          </view>\n          <text class=\"item-desc\">学校澡堂洗澡要用</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport {\n  onShareAppMessage\n} from '@dcloudio/uni-app';\nimport {\n  ref\n} from 'vue';\n\n// 假设有一个bannerList用于分享图片，这里提供一个默认值\nconst bannerList = ref([{\n  src: 'https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南'\n}]); // 请替换为实际的图片URL\n\n// 微信小程序分享配置\n// 当用户点击右上角菜单中的“转发”按钮时，会调用此函数\nonShareAppMessage(() => {\n  return {\n    title: '河南农业大学新生指南中心', // 分享卡的标题\n    path: '/pages/index/index', // 分享后用户点击进入的页面路径，请确保路径正确\n    imageUrl: bannerList.value[0].src, // 分享卡上显示的图片，这里使用第一张轮播图\n  };\n});\n\n/**\n * 导航到web-view页面，用于加载外部链接\n * @param {string} externalUrl 外部链接URL\n */\nconst navigateToWebview = (externalUrl) => {\n  // 对URL进行编码，以便在跳转时作为参数传递\n  const encodedUrl = encodeURIComponent(externalUrl);\n  // *** 关键修改：将路径从 /pages/webview/webview 改为 /pages/web-view/web-view ***\n  uni.navigateTo({\n    url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n// 使用scss可以更好地组织和管理样式\n.page-container {\n  padding: 30rpx;\n  background-color: #f0f4f7; // 页面背景色调整为更柔和的浅蓝色\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.header-title {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #2c3e50; // 深灰色标题\n}\n\n.card-section {\n  margin-bottom: 40rpx;\n}\n\n.card {\n  background-color: #fff;\n  border-radius: 24rpx; // 边角更圆润\n  padding: 30rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08); // 更明显的阴影效果\n  transition: transform 0.3s ease; // 添加过渡效果\n  &:active {\n    transform: translateY(2rpx); // 点击时轻微下沉\n  }\n}\n\n.card-title-container {\n  position: relative;\n  margin-bottom: 30rpx;\n  padding-left: 20rpx;\n  display: flex;\n  align-items: center;\n}\n\n.card-title-container::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 10rpx; // 强调条更宽\n  height: 90%; // 强调条更高\n  background-color: #6699CC; // 柔和的蓝色强调条\n  border-radius: 5rpx;\n}\n\n.card-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #34495e; // 标题颜色\n  margin-left: 10rpx; // 标题与强调条间距\n}\n.text{\r\n\tcolor: #55aa00;\r\n}\r\n\n.list-item {\n  display: flex;\n  flex-direction: column; // 保持主轴为列，标题和描述上下排列\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #e9ecef; // 更细的分割线\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.item-header {\n  display: flex; // 内部使用flex布局，让图标和标题并排\n  align-items: center; // 垂直居中对齐\n  margin-bottom: 5rpx; // 标题和描述之间留一点间距\n}\n\n.item-icon {\r\n\tborder-radius: 10px;\n  width: 48rpx; // 图标大小\n  height: 48rpx; // 图标大小\n  margin-right: 15rpx; // 图标与文字的间距\n  flex-shrink: 0; // 防止图标被压缩\n}\n\n.item-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  line-height: 1.5;\n  flex: 1; // 标题占据剩余空间\n}\n\n.item-desc {\n  font-size: 28rpx;\n  color: #888; // 描述文字颜色更柔和\n  line-height: 1.5;\n  margin-top: 5rpx;\n  padding-left: 63rpx; /* 适配图标宽度 + margin-right，使描述文本对齐标题 */\n}\n\n.platform-item {\n  display: flex;\n  flex-direction: column;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #e9ecef;\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.link-group {\n  margin-top: 10rpx;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n}\n\n.link-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #e0f0ff; // 浅蓝色背景\n  color: #4a77a8; // 链接文字颜色\n  font-size: 28rpx;\n  padding: 16rpx 30rpx; // 按钮更大，更饱满\n  border-radius: 16rpx; // 按钮边角更圆润\n  margin-top: 10rpx;\n  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease; // 添加过渡效果\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1); // 按钮阴影\n  border: 1rpx solid rgba(74, 119, 168, 0.2); // 浅色边框\n  &:active {\n    background-color: #cce0ff; // 点击时变深\n    transform: scale(0.97) translateY(2rpx); // 点击时轻微缩小并下沉\n    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.15); // 点击时阴影变小\n  }\n}\n\n.note-text {\n  font-size: 24rpx;\n  color: #777; // 备注文字颜色\n  margin-top: 10rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/changyong/changyong.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShareAppMessage", "uni"], "mappings": ";;;;;AA2GA,UAAA,aAAAA,cAAA,IAAA,CAAA;AAAA,MACA,KAAA;AAAA,IACA,CAAA,CAAA;AAIAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA;AAAA;AAAA,QACA,MAAA;AAAA;AAAA,QACA,UAAA,WAAA,MAAA,CAAA,EAAA;AAAA;AAAA,MACA;AAAA,IACA,CAAA;AAMA,UAAA,oBAAA,CAAA,gBAAA;AAEA,YAAA,aAAA,mBAAA,WAAA;AAEAC,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,oDAAA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;ACnIA,GAAG,WAAW,eAAe;"}