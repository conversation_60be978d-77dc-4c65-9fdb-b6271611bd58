<template>
	<view class="page-container">
		<button class="contributor-button" @tap="showContributors">特别鸣谢</button>

		<view class="content-wrapper">
			<view class="header-section">
				<text class="section-title">问答专栏</text>
			</view>
			<view class="qa-item" v-for="(item, index) in registrationQa" :key="'reg-' + index">
				<text class="question">{{ item.question }}</text>
				<text class="answer">
					<text v-for="(part, pIndex) in parseAnswer(item.answer)" :key="pIndex" :class="{ 'highlight': part.type === 'highlight' }">{{ part.content }}</text>
				</text>
				
				<view v-if="item.image_url != null" @tap=previewImage(item.image_url)>
					<img class="image" :src=item.image_url  alt="校历图片" />
				</view>
				
			</view>
		</view>

		<view class="custom-modal-overlay" v-if="showContributorModal" @tap="closeContributorsModal">
			<view class="custom-modal-content" @tap.stop>
				<text class="modal-title">贡献者名单(不分先后)</text>
				
				<view class="modal-contributors-list">
					<!-- 遍历贡献者类别 -->
					<view v-for="(category, catIndex) in contributors" :key="'cat-' + catIndex" class="contributor-category">
						<text class="category-title">{{ category.category }}</text>
						<view class="names-grid">
							<text class="contributor-name" v-for="(name, nameIndex) in category.names" :key="'name-' + nameIndex">{{ name }}</text>
						</view>
					</view>
				</view>
				<button class="modal-button" @tap="closeContributorsModal">关闭</button>
			</view>
		</view>

		<view class="scroll-controls">
			<text class="reading-progress">{{ Math.round(readingProgress) }}%</text>
			<button class="back-to-top-button" @click="scrollToTop">
				<image class="top-arrow-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6893244343dd4_1754473539.png"></image>
			</button>
		</view>
		
		<view class="bottom">
			技术支持 河南农业大学IT工作室
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { onPageScroll, onReady } from '@dcloudio/uni-app';
import qa from './Q&A.json'

// 贡献者名单弹窗逻辑
const showContributorModal = ref(false);
// 动态贡献者名单，现在分为类别
const contributors = ref([
	{ category: '河南农业大学IT工作室成员', names: ['24级CLOWN','24级千木','24级非酋sama','24级XX','24级斯特','24级阿布学长','24级槿熙','24级昔文', '24级Pinging'] },
	{ category: '其他贡献者', names: ['22级卢荟胶', '23级鲤鱼'] }
]);

const showContributors = () => {
	showContributorModal.value = true;
};
const closeContributorsModal = () => {
	showContributorModal.value = false;
};

// 阅读进度和返回顶部逻辑
const readingProgress = ref(0);
let totalScrollHeight = 0; // 页面总可滚动高度



onReady(() => {
	// 页面渲染完成后，获取内容区域的总高度
	uni.createSelectorQuery().select('.page-container').boundingClientRect(pageRect => {
		if (pageRect) {
			// totalScrollHeight = 实际内容高度 - 视口高度
			totalScrollHeight = pageRect.height - uni.getSystemInfoSync().windowHeight;
			if (totalScrollHeight < 0) totalScrollHeight = 0; // 防止负值
		}
	}).exec();
});

onPageScroll((e) => {
	if (totalScrollHeight > 0) {
		readingProgress.value = (e.scrollTop / totalScrollHeight) * 100;
		if (readingProgress.value > 100) readingProgress.value = 100; // 确保不超过100%
	} else {
		readingProgress.value = 0;
	}
});

const scrollToTop = () => {
	uni.pageScrollTo({
		scrollTop: 0,
		duration: 300
	});
};

// 解析答案字符串，将高亮部分分离
const parseAnswer = (answerString) => {
    const parts = [];
    const regex = /<text class="highlight">(.*?)<\/text>/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(answerString)) !== null) {
        // Add plain text before the highlight
        if (match.index > lastIndex) {
            parts.push({ type: 'text', content: answerString.substring(lastIndex, match.index) });
        }
        // Add highlight text
        parts.push({ type: 'highlight', content: match[1] });
        lastIndex = regex.lastIndex;
    }

    // Add any remaining plain text after the last highlight
    if (lastIndex < answerString.length) {
        parts.push({ type: 'text', content: answerString.substring(lastIndex) });
    }
    return parts;
};

// 问答数据 (已从模板中提取到 script 部分，便于管理和维护)
const registrationQa = ref(qa);



// 图片预览逻辑已修改为跳转到详情页
const previewImage = (imageUrl) => {
	uni.navigateTo({
		url: `/pages/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`,
	});
};


</script>

<style scoped>
/* 页面容器 */
.page-container {
	width: 100%;
	min-height: 100vh; /* 确保页面高度至少为视口高度 */
	box-sizing: border-box;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
}


.bottom{
	font-size: 15px;
	font-weight: 100;
	text-align: center;
	color: #7a776f;
}

/* 内容区域包裹器 */
.content-wrapper {
	width: 90%;
	margin: 0 auto;
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
	border-radius: 16rpx; /* 圆角 */
	/* 调整内容区域的内边距，左右各增加到30rpx */
	padding: 100rpx 30rpx 30rpx 30rpx; /* 上、右、下、左 */
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

.contributor-button {
	position: absolute; /* 固定定位 */
	top: 60rpx; /* 距离顶部60rpx */
	right: 30rpx; /* 距离右侧30rpx */
	background-color: transparent; /* 透明背景 */
	color: #000000; /* 黑色文字 */
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border: 2rpx solid #000000; /* 黑色边框 */
	border-radius: 40rpx; /* 圆角 */
	line-height: 1; /* 调整行高使按钮文字垂直居中 */
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
	z-index: 100; /* 确保在内容之上 */
}

.contributor-button:active,
.contributor-button:hover {
	background-color: #000000; /* 悬停/点击时的背景色 */
	color: #ffffff; /* 悬停/点击时的文字颜色 */
	transform: scale(1.05);
}

/* 标题样式 */
.header-section {
	margin: 100px;
	text-align: center;
	margin: 40rpx auto 30rpx; /* 上下边距，左右居中 */
	border-bottom: 2rpx solid #338174; /* 底部边框 */
	padding-bottom: 10rpx; /* 边框与文字间距 */
	display: table; /* 使边框只包裹内容宽度 */
}
.image{
	width: 100%;
}

.section-title {
	margin: 100px;
	font-size: 33.6rpx;
	font-weight: bold;
	color: #000000; /* 标题颜色改为深蓝色，与主题色保持一致 */
	line-height: 1.75;
	white-space: nowrap; /* 防止标题换行 */
}

/* 问答项样式 */
.qa-item {
	margin: 30rpx 0;
	line-height: 1.75;
	font-size: 28rpx;
	letter-spacing: 0.05em;
	color: #333333;
	text-align: justify;
}

.question {
	font-weight: bold;
	color: #55aa00; /* 绿色问题 */
	display: block;
	margin-bottom: 10rpx;
}

.answer {
	display: block;
}

.highlight {
	color: #0f4c81; /* 高亮颜色改为深蓝色，与主题色保持一致 */
	font-weight: bold;
}




/* 贡献者名单弹窗样式 */
.custom-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7); /* 更深的半透明背景，突出弹窗 */
	z-index: 101;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: fadeIn 0.3s ease-out;
}

.custom-modal-content {
	background-color: #ffffff;
	border-radius: 28rpx; /* 稍微更大的圆角 */
	padding: 60rpx; /* 增加内边距 */
	margin: 40rpx;
	max-width: 650rpx; /* 稍微增加最大宽度 */
	width: 90%;
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.25); /* 更明显的阴影 */
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	animation: slideIn 0.3s ease-out;
	border: 1rpx solid rgba(200, 200, 200, 0.3); /* 增加一个细微的边框 */
}

.modal-title {
	font-size: 42rpx; /* 标题字号更大 */
	font-weight: bold;
	color: #000000;
	margin-bottom: 35rpx; /* 增加与列表的间距 */
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.05); /* 标题轻微文字阴影 */
}

/* 新增：贡献者类别容器 */
.contributor-category {
	width: 100%;
	margin-bottom: 40rpx; /* 类别之间的间距 */
}

/* 新增：类别标题 */
.category-title {
	font-size: 32rpx; /* 类别标题字号 */
	font-weight: bold;
	color: #609c63; /* 类别标题颜色 */
	margin-bottom: 20rpx; /* 标题与名字的间距 */
	display: block; /* 确保标题独占一行 */
	text-align: center;
	border-bottom: 2rpx solid #e0e0e0; /* 底部细线 */
	padding-bottom: 10rpx;
}

/* 新增：名字网格布局 */
.names-grid {
	display: flex;
	flex-wrap: wrap; /* 允许换行 */
	justify-content: center; /* 名字居中对齐 */
	gap: 20rpx 30rpx; /* 行间距和列间距 */
}

.contributor-name {
	font-size: 30rpx; /* 贡献者名字字号稍大 */
	color: #4a4a4a; /* 更深的灰色 */
	/* 移除原来的 margin，使用 gap 控制间距 */
	line-height: 1.6;
	padding: 5rpx 0; /* 增加垂直内边距 */
	transition: color 0.2s ease;
	/* white-space: nowrap; */ /* 允许名字内部换行，如果名字过长 */
}


.modal-button {
	background:#609c63; 
	color: #fff;
	border-radius: 40rpx;
	font-size: 28rpx; /* 按钮文字稍大 */
	height: 80rpx; /* 按钮高度增加 */
	width: 85%; /* 按钮宽度稍大 */
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 20rpx rgba(15, 76, 129, 0.4); /* 更明显的阴影，与背景色匹配 */
	transition: all 0.3s ease-in-out; /* 更平滑的过渡效果 */
	border: none;
	letter-spacing: 1rpx; /* 增加文字间距 */
}

.modal-button:active {
	transform: translateY(4rpx); /* 点击时下沉效果更明显 */
	box-shadow: 0 4rpx 10rpx rgba(15, 76, 129, 0.3); /* 点击时阴影变小 */
}













/* 返回顶部按钮和阅读进度样式 */
.scroll-controls {
	position: fixed;
	bottom: 130rpx; /* 距离底部 */
	right: 20rpx; /* 距离右侧 */
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 99; /* 确保在内容之上，但在弹窗之下 */
}

.reading-progress {
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 30rpx;
	margin-bottom: 10rpx;
	white-space: nowrap; /* 防止百分比换行 */
}

.back-to-top-button {
	background-color: #0f4c81; /* 匹配主题色 */
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%; /* 圆形按钮 */
	display: flex; /* 使用flexbox来居中图片 */
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
	border: none;
	padding: 0; /* 移除默认padding */
}

.top-arrow-icon {
	width: 80rpx; /* 图片大小 */
	height: 80rpx;
	/* 可以添加滤镜来改变颜色，如果图片是黑色的 */
	/* filter: invert(100%); */
}

/* 动画效果 (弹窗) */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideIn {
	from {
		transform: translateY(-50px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
</style>
