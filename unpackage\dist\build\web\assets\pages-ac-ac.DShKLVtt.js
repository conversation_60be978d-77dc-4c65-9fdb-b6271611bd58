import{r as e,p as s,q as a,k as o,c as n,w as r,j as t,o as c,a as l,l as i,m as d}from"./index-DeHPfleJ.js";import{_ as p}from"./home.LEWUQR7v.js";import{_ as u,c as m,d as w,e as _,o as x,a as f}from"./_plugin-vue_export-helper.DFaavnJe.js";const v=u({__name:"ac",setup(u){const v=e("");s((()=>{const e=a(),s=e[e.length-1].options;s.webviewUrl&&(v.value=decodeURIComponent(s.webviewUrl))})),m((()=>{o({url:"/pages/index/index"})})),w((()=>{})),_((e=>{}));return x((e=>{const s=e.webViewUrl;return{path:`/pages/index/index?webViewUrl=${encodeURIComponent(s)}`}})),f((()=>{})),(e,s)=>{const a=i,u=d,m=t;return c(),n(m,{src:v.value},{default:r((()=>[l(u,{class:"close-view",onClick:s[0]||(s[0]=e=>{o({url:"/pages/index/index"})})},{default:r((()=>[l(a,{class:"close-icon",src:p})])),_:1})])),_:1},8,["src"])}}},[["__scopeId","data-v-3dda6b0e"]]);export{v as default};
