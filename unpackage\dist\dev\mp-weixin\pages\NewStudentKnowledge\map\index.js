"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const images = common_vendor.ref([
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg",
        name: "许昌校区",
        description: "新建校区，现代化教学设施"
      },
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg",
        name: "龙子湖校区",
        description: "主校区，位于郑州市龙子湖高校园区"
      },
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png",
        name: "文化路校区",
        description: "百年老校区，省级文物保护单位"
      }
    ]);
    const previewImage = (imageUrl) => {
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`
      });
    };
    const showCustomModal = common_vendor.ref(false);
    const closeCustomModal = () => {
      showCustomModal.value = false;
    };
    const copy3DMapLink = () => {
      const mapUrl = "https://www.720yun.com/vr/315z05drknk?s=332068";
      common_vendor.index.setClipboardData({
        data: mapUrl,
        success: function() {
          showCustomModal.value = true;
        },
        fail: function() {
          common_vendor.index.showToast({
            title: "复制失败，请手动复制",
            icon: "none"
          });
        }
      });
    };
    const navigateToLandscape = () => {
      common_vendor.index.navigateTo({
        url: "/pages/NewStudentKnowledge/landscape/landscape"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(images.value, (item, index, i0) => {
          return {
            a: item.src,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.description),
            d: index,
            e: common_vendor.o(($event) => previewImage(item.src), index)
          };
        }),
        b: common_vendor.o(copy3DMapLink),
        c: common_vendor.o(navigateToLandscape),
        d: showCustomModal.value
      }, showCustomModal.value ? {
        e: common_vendor.o(closeCustomModal),
        f: common_vendor.o(() => {
        }),
        g: common_vendor.o(closeCustomModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4150081"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/map/index.js.map
