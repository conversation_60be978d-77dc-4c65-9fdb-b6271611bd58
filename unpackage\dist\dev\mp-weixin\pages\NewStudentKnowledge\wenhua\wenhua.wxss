/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 返回顶部按钮和阅读进度样式 */
.scroll-controls.data-v-d2414581 {
  position: fixed;
  bottom: 130rpx;
  /* 距离底部 */
  right: 20rpx;
  /* 距离右侧 */
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 99;
  /* 确保在内容之上，但在弹窗之下 */
}
.reading-progress.data-v-d2414581 {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
  white-space: nowrap;
  /* 防止百分比换行 */
}
.back-to-top-button.data-v-d2414581 {
  background-color: #0f4c81;
  /* 匹配主题色 */
  color: #fff;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  /* 圆形按钮 */
  display: flex;
  /* 使用flexbox来居中图片 */
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  border: none;
  padding: 0;
  /* 移除默认padding */
}
.top-arrow-icon.data-v-d2414581 {
  width: 80rpx;
  /* 图片大小 */
  height: 80rpx;
  /* 可以添加滤镜来改变颜色，如果图片是黑色的 */
  /* filter: invert(100%); */
}
.container.data-v-d2414581 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
  /* 浅灰色背景 */
}
.status-bar-placeholder.data-v-d2414581 {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #ffffff;
}
.main-scroll-view.data-v-d2414581 {
  flex: 1;
  box-sizing: border-box;
}
.content-wrapper.data-v-d2414581 {
  width: 100%;
  max-width: 750px;
  /* 限制内容最大宽度 */
  margin: 0 auto;
  background-color: #ffffff;
  padding: 20px;
  /* 增加内边距 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
  font-size: 15px;
  /* 基础字体大小 */
  line-height: 1.8;
  /* 增加行高 */
  color: #333333;
  border-radius: 10px;
  /* 圆角 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  /* 柔和阴影 */
}

/* 主标题样式 */
.main-title-wrapper.data-v-d2414581 {
  text-align: center;
  margin: 2em auto 1.5em;
  padding: 0.5em 1em;
  border-bottom: 2px solid #0a7aaf;
  /* 原 HTML 中的蓝色边框 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: -webkit-fit-content;
  width: fit-content;
  margin-top: 0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.05);
  /* 文本阴影 */
}
.main-title.data-v-d2414581 {
  font-size: 20px;
  /* 调整字体大小 */
  font-weight: bold;
  line-height: 1.5;
  color: #333333;
  white-space: nowrap;
}

/* 介绍段落 */
.intro-paragraph.data-v-d2414581 {
  text-align: justify;
  margin: 1.5em 0;
  letter-spacing: 0.02em;
  line-height: 1.75;
}
.intro-text.data-v-d2414581 {
  font-size: 15px;
  color: #333333;
  line-height: 1.75;
  letter-spacing: 0.02em;
  display: block;
}

/* 章节标题样式 (e.g., 入学准备篇) */
.section-heading-wrapper.data-v-d2414581 {
  text-align: center;
  margin: 3.5em auto 2em;
  /* 调整间距 */
  padding: 0.6em 1.2em;
  /* 调整内边距 */
  background: #0a7aaf;
  /* 蓝色背景 */
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  /* 阴影更明显 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: -webkit-fit-content;
  width: fit-content;
}
.section-heading.data-v-d2414581 {
  font-size: 18px;
  /* 调整字体大小 */
  line-height: 1.5;
  color: #fff;
  white-space: nowrap;
}

/* 子章节标题样式 (e.g., 证件与材料) */
.sub-section-title-wrapper.data-v-d2414581 {
  text-align: left;
  padding-left: 12px;
  border-left: 4px solid #0a7aaf;
  /* 蓝色左边框 */
  margin: 2.5em 0 1em 0;
  /* 调整间距 */
  border-bottom: 1px dashed rgba(10, 122, 175, 0.5);
  /* 蓝色虚线底边框 */
  padding-bottom: 5px;
  /* 虚线与文字的间距 */
}
.sub-section-title.data-v-d2414581 {
  font-size: 17px;
  /* 调整字体大小 */
  line-height: 1.3;
  font-weight: bold;
  color: #555555;
  /* 更柔和的颜色 */
}

/* 子子章节标题样式 (e.g., 从郑州火车站到文化路校区) */
.sub-sub-section-title-wrapper.data-v-d2414581 {
  margin: 2em 0 0.8em 0;
  /* 调整间距 */
  text-align: left;
}
.sub-sub-section-title.data-v-d2414581 {
  font-size: 16px;
  /* 调整字体大小 */
  line-height: 1.6;
  color: #0a7aaf;
  /* 蓝色 */
  font-weight: bold;
}

/* 信息列表样式 (ul/li 转换) */
.info-list.data-v-d2414581 {
  list-style: none;
  /* 移除默认列表样式 */
  padding-left: 0;
  margin-left: 0;
  line-height: 1.75;
}
.list-item.data-v-d2414581 {
  display: flex;
  /* 使用 flex 布局实现项目符号和内容的对齐 */
  margin: 0.6em 0;
  /* 增加列表项之间的间距 */
  align-items: flex-start;
  /* 顶部对齐 */
}
.list-bullet.data-v-d2414581 {
  font-size: 15px;
  line-height: 1.75;
  margin-right: 0.6em;
  /* 项目符号和内容之间的间距 */
  flex-shrink: 0;
  /* 防止项目符号被压缩 */
  color: #0a7aaf;
  /* 项目符号颜色 */
  font-weight: bold;
}
.list-number.data-v-d2414581 {
  font-size: 15px;
  line-height: 1.75;
  margin-right: 0.6em;
  /* 数字和内容之间的间距 */
  flex-shrink: 0;
  color: #0a7aaf;
  /* 数字颜色 */
  font-weight: bold;
}
.list-text.data-v-d2414581 {
  flex: 1;
  /* 内容占据剩余空间 */
  font-size: 15px;
  color: #333333;
  line-height: 1.75;
  letter-spacing: 0.02em;
  /* 稍微调整字间距 */
  display: block;
}
.ordered-list.data-v-d2414581 {
  padding-left: 0;
  margin-left: 0;
  line-height: 1.75;
}

/* 普通信息段落样式 */
.info-paragraph.data-v-d2414581 {
  margin: 1.5em 0;
  /* 调整上下间距 */
  letter-spacing: 0.02em;
  line-height: 1.75;
  text-align: justify;
  /* 两端对齐 */
}

/* 粗体文本样式 */
.info-strong.data-v-d2414581 {
  font-weight: bold;
  color: #333333;
  /* 默认粗体为深色 */
}

/* 蓝色粗体文本样式 */
.info-strong-blue.data-v-d2414581 {
  font-weight: bold;
  color: #0a7aaf;
}

/* 自定义换行符样式，用于模拟 <br/> */
.newline.data-v-d2414581 {
  display: block;
  height: 0.5em;
  /* 控制换行间距 */
  content: "";
}

/* Blockquote 样式 */
.blockquote-wrapper.data-v-d2414581 {
  padding: 1em 1em 1em 2em;
  border-left: 4px solid #0a7aaf;
  border-radius: 6px;
  background-color: #fefefe;
  /* 浅色背景 */
  margin-bottom: 1.5em;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  /* 柔和阴影 */
  color: rgba(0, 0, 0, 0.7);
  /* 文本颜色 */
}
.blockquote-text.data-v-d2414581 {
  font-size: 15px;
  line-height: 1.75;
  letter-spacing: 0.02em;
  display: block;
}
.blockquote-sub-title.data-v-d2414581 {
  font-size: 16px;
  line-height: 1.75;
  font-weight: bold;
  color: #0a7aaf;
  display: block;
  margin-bottom: 0.5em;
}

/* 链接文本样式 */
.link-text.data-v-d2414581 {
  color: #000000;
  /* 原 HTML 中的链接颜色 */
  font-size: 15px;
  line-height: 1.75;
}

/* 底部段落的特殊样式 */
.final-paragraph.data-v-d2414581 {
  margin-top: 3em;
  text-align: center;
  font-size: 15px;
  color: #666666;
  font-weight: bold;
}